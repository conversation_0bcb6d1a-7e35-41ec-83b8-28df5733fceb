(()=>{var e={};e.id=974,e.ids=[974],e.modules={682:(e,s,r)=>{Promise.resolve().then(r.bind(r,1204))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1204:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI forsikrings guiden\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx","default")},1306:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=r(5239),a=r(8088),n=r(8170),l=r.n(n),i=r(893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let o=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Projects\\AI forsikrings guiden\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6859:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(687),a=r(3210),n=r.n(a);function l({onSendMessage:e,disabled:s=!1}){let[r,n]=(0,a.useState)(""),[l,i]=(0,a.useState)(!1),d=async t=>{t.preventDefault(),!r.trim()||l||s||(i(!0),await e(r.trim()),n(""),i(!1))};return(0,t.jsxs)("form",{onSubmit:d,className:"flex space-x-2",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)("textarea",{value:r,onChange:e=>n(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||s||(e.preventDefault(),d(e))},placeholder:s?"Chat er midlertidigt utilg\xe6ngelig...":"Skriv dit forsikringssp\xf8rgsm\xe5l her...",className:"input-field resize-none h-12",rows:1,disabled:l||s})}),(0,t.jsx)("button",{type:"submit",disabled:!r.trim()||l||s,className:"btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:l?(0,t.jsxs)("svg",{className:"animate-spin h-5 w-5",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):"Send"})]})}class i extends n().Component{constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("ErrorBoundary caught an error:",e,s)}render(){if(this.state.hasError){let e=this.props.fallback||d;return(0,t.jsx)(e,{error:this.state.error,resetError:this.resetError})}return this.props.children}}function d({error:e,resetError:s}){return(0,t.jsxs)("div",{className:"card max-w-2xl mx-auto text-center",children:[(0,t.jsx)("div",{className:"text-red-500 text-4xl mb-4",children:"⚠️"}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Der opstod en uventet fejl"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Beklager, noget gik galt. Pr\xf8v venligst igen eller kontakt support hvis problemet forts\xe6tter."}),!1,(0,t.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,t.jsx)("button",{onClick:s,className:"btn-primary",children:"Pr\xf8v igen"}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"btn-secondary",children:"Genindl\xe6s siden"})]})]})}function o({error:e,resetError:s}){return(0,t.jsxs)("div",{className:"border-2 border-red-200 rounded-lg p-6 text-center bg-red-50",children:[(0,t.jsx)("div",{className:"text-red-500 text-2xl mb-3",children:"\uD83E\uDD16\uD83D\uDCA5"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"Chat fejl"}),(0,t.jsx)("p",{className:"text-red-700 mb-4",children:"AI-assistenten kunne ikke svare. Dette kan skyldes netv\xe6rksproblemer eller API-fejl."}),(0,t.jsx)("button",{onClick:s,className:"bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors",children:"Pr\xf8v chat igen"})]})}function c({size:e="md",text:s="Indl\xe6ser...",className:r=""}){return(0,t.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[(0,t.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} animate-spin`,children:(0,t.jsxs)("svg",{className:"w-full h-full text-insurance-blue",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),s&&(0,t.jsx)("p",{className:`mt-2 text-gray-600 ${{sm:"text-sm",md:"text-base",lg:"text-lg"}[e]}`,children:s})]})}function m(){return(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500",children:[(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-insurance-blue rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-insurance-blue rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-insurance-blue rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),(0,t.jsx)("span",{className:"text-sm",children:"AI t\xe6nker..."})]})}function x(){return(0,t.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,t.jsx)(c,{size:"lg",text:"Behandler dokument..."})})}function u(){let[e,s]=(0,a.useState)([{id:"1",content:"Hej! Jeg er din AI forsikringsassistent. Hvordan kan jeg hj\xe6lpe dig i dag?",role:"assistant",timestamp:new Date}]),[r,n]=(0,a.useState)(!1),[d,c]=(0,a.useState)(null),x=async r=>{let t={id:Date.now().toString(),content:r,role:"user",timestamp:new Date};s(e=>[...e,t]),n(!0),c(null);try{let r=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...e,t].map(e=>({role:e.role,content:e.content})),userDocuments:[]})}),a=await r.json();if(!r.ok){if(500===r.status&&a.error?.includes("API key"))throw Error("OpenAI API n\xf8gle er ikke konfigureret. Kontakt administrator.");if(401===r.status)throw Error("Ugyldig API n\xf8gle. Kontakt administrator.");if(429===r.status)throw Error("For mange foresp\xf8rgsler. Pr\xf8v igen om lidt.");throw Error(a.error||"Ukendt fejl fra serveren")}let n={id:(Date.now()+1).toString(),content:a.message,role:"assistant",timestamp:new Date};s(e=>[...e,n])}catch(t){console.error("Error sending message:",t);let e=t instanceof Error?t.message:"Der opstod en uventet fejl";c(e);let r={id:(Date.now()+1).toString(),content:`❌ ${e}`,role:"assistant",timestamp:new Date};s(e=>[...e,r])}finally{n(!1)}};return(0,t.jsx)(i,{fallback:o,children:(0,t.jsxs)("div",{className:"card max-w-4xl mx-auto",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Chat med AI Forsikringsassistent"}),d&&(0,t.jsx)("div",{className:"mb-4 p-3 bg-red-100 border border-red-300 rounded-lg",children:(0,t.jsxs)("p",{className:"text-red-700 text-sm",children:[(0,t.jsx)("strong",{children:"Fejl:"})," ",d]})}),(0,t.jsxs)("div",{className:"h-96 overflow-y-auto mb-4 border rounded-lg p-4 bg-gray-50",children:[e.map(e=>(0,t.jsx)("div",{className:`mb-4 ${"user"===e.role?"text-right":"text-left"}`,children:(0,t.jsxs)("div",{className:`inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${"user"===e.role?"bg-insurance-blue text-white":"bg-white text-gray-900 border"}`,children:[(0,t.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:e.content}),(0,t.jsx)("p",{className:"text-xs opacity-70 mt-1",children:e.timestamp.toLocaleTimeString("da-DK",{hour:"2-digit",minute:"2-digit"})})]})},e.id)),r&&(0,t.jsx)("div",{className:"mb-4 text-left",children:(0,t.jsx)("div",{className:"inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-white border",children:(0,t.jsx)(m,{})})})]}),(0,t.jsx)(l,{onSendMessage:x,disabled:r})]})})}function h({onDocumentParsed:e}){let[s,r]=(0,a.useState)(!1),[n,l]=(0,a.useState)(null),i=async s=>{let t=s.target.files?.[0];if(t){if(t.size>0xa00000){l("Filen er for stor. Maksimal st\xf8rrelse er 10MB."),s.target.value="";return}r(!0),l(null);try{let s="";if("application/pdf"===t.type)s=`[PDF Dokument: ${t.name}]
Dette er en PDF fil. Indhold vil blive behandlet n\xe5r backend PDF parsing er implementeret.

Filst\xf8rrelse: ${(t.size/1024/1024).toFixed(2)} MB`;else if("text/plain"===t.type)(s=await t.text()).length>5e4&&(s=s.substring(0,5e4)+"\n\n[Tekst afkortet - for lang til visning]");else if(t.type.startsWith("image/"))s=`[Billede: ${t.name}]
Dette er et billede. OCR behandling vil blive implementeret for at udtr\xe6kke tekst.

Filst\xf8rrelse: ${(t.size/1024/1024).toFixed(2)} MB`;else throw Error("Ikke-underst\xf8ttet filtype. Upload venligst PDF, tekst eller billede filer.");let r=d(t.name,s);e({name:t.name,content:s,type:r})}catch(e){console.error("Upload error:",e),l(e instanceof Error?e.message:"Der opstod en fejl ved upload")}finally{r(!1),s.target.value=""}}},d=(e,s)=>{let r=e.toLowerCase()+" "+s.toLowerCase();return r.includes("police")||r.includes("forsikring")||r.includes("d\xe6kning")?"insurance_policy":r.includes("skade")||r.includes("anmeldelse")||r.includes("erstatning")?"claim_document":r.includes("korrespondance")||r.includes("brev")||r.includes("mail")?"correspondence":"general_document"};return s?(0,t.jsx)("div",{className:"border-2 border-dashed border-insurance-blue rounded-lg p-6",children:(0,t.jsx)(x,{})}):(0,t.jsx)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${n?"border-red-300 bg-red-50":"border-gray-300 hover:border-insurance-blue"}`,children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:`mx-auto w-12 h-12 ${n?"text-red-400":"text-gray-400"}`,children:n?(0,t.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 48 48",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"})}):(0,t.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 48 48",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"})})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"file-upload",className:"cursor-pointer",children:[(0,t.jsx)("span",{className:`text-sm font-medium hover:opacity-80 ${n?"text-red-600":"text-insurance-blue hover:text-insurance-dark"}`,children:n?"Pr\xf8v igen":"Upload forsikringsdokument"}),(0,t.jsx)("input",{id:"file-upload",name:"file-upload",type:"file",className:"sr-only",accept:".pdf,.txt,.jpg,.jpeg,.png",onChange:i,disabled:s})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"PDF, tekst eller billede filer op til 10MB"})]}),s&&(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsxs)("svg",{className:"animate-spin h-4 w-4 text-insurance-blue",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Behandler dokument..."})]}),n&&(0,t.jsx)("div",{className:"text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2",children:n})]})})}function g({documents:e,onDocumentSelect:s,highlightTerms:r=[]}){let[n,l]=(0,a.useState)(e.length>0?e[0]:null),[i,d]=(0,a.useState)(""),o=e=>{l(e),s?.(e)},c=e=>{switch(e){case"insurance_policy":return"\uD83D\uDCCB";case"claim_document":return"\uD83D\uDD27";case"correspondence":return"✉️";default:return"\uD83D\uDCC4"}},m=e=>{switch(e){case"insurance_policy":return"Forsikringspolice";case"claim_document":return"Skadeanmeldelse";case"correspondence":return"Korrespondance";default:return"Dokument"}};return 0===e.length?(0,t.jsxs)("div",{className:"card text-center py-8",children:[(0,t.jsx)("div",{className:"text-gray-400 text-4xl mb-4",children:"\uD83D\uDCC4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Ingen dokumenter uploadet"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Upload forsikringsdokumenter for at f\xe5 personaliseret hj\xe6lp"})]}):(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,t.jsxs)("div",{className:"lg:w-1/3",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:["Mine Dokumenter (",e.length,")"]}),(0,t.jsx)("div",{className:"space-y-2",children:e.map((e,s)=>(0,t.jsx)("button",{onClick:()=>o(e),className:`w-full text-left p-3 rounded-lg border transition-colors ${n?.name===e.name?"border-insurance-blue bg-insurance-light":"border-gray-200 hover:border-gray-300"}`,children:(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)("span",{className:"text-lg",children:c(e.type)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:m(e.type)})]})]})},s))})]}),(0,t.jsx)("div",{className:"lg:w-2/3",children:n&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:n.name}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsx)("input",{type:"text",placeholder:"S\xf8g i dokument...",value:i,onChange:e=>d(e.target.value),className:"text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-1 focus:ring-insurance-blue focus:border-insurance-blue"})})]}),(0,t.jsx)("div",{className:"bg-gray-50 border rounded-lg p-4 max-h-96 overflow-y-auto",children:(0,t.jsx)("div",{className:"text-sm text-gray-700 whitespace-pre-wrap leading-relaxed",dangerouslySetInnerHTML:{__html:((e,s)=>{if(!s)return e;let r=RegExp(`(${s})`,"gi");return e.replace(r,'<mark class="bg-blue-200 px-1 rounded font-semibold">$1</mark>')})(((e,s)=>{if(0===s.length)return e;let r=e;return s.forEach(e=>{let s=RegExp(`(${e})`,"gi");r=r.replace(s,'<mark class="bg-yellow-200 px-1 rounded">$1</mark>')}),r})(n.content,r),i)}})}),(0,t.jsxs)("div",{className:"mt-4 flex items-center justify-between text-xs text-gray-500",children:[(0,t.jsxs)("span",{children:["Type: ",m(n.type)]}),n.uploadDate&&(0,t.jsxs)("span",{children:["Uploadet: ",n.uploadDate.toLocaleDateString("da-DK")]})]})]})})]})})}function p(){let[e,s]=(0,a.useState)([]),[r,n]=(0,a.useState)(null),[l,i]=(0,a.useState)("chat"),d=e=>{let r={...e,uploadDate:new Date};s(e=>[...e,r])};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8 text-center",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Velkommen til AI Forsikringsguiden"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"Intelligent forsikringsr\xe5dgivning p\xe5 dansk. F\xe5 svar p\xe5 dine sp\xf8rgsm\xe5l, analyser dokumenter, og f\xe5 hj\xe6lp til forsikringssp\xf8rgsm\xe5l."})]}),(0,t.jsxs)("div",{className:"flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm border w-fit mx-auto",children:[(0,t.jsx)("button",{onClick:()=>i("chat"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"chat"===l?"bg-insurance-blue text-white":"text-gray-600 hover:text-gray-900"}`,children:"\uD83D\uDCAC Chat"}),(0,t.jsx)("button",{onClick:()=>i("dashboard"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"dashboard"===l?"bg-insurance-blue text-white":"text-gray-600 hover:text-gray-900"}`,children:"\uD83D\uDCCA Dashboard"}),(0,t.jsxs)("button",{onClick:()=>i("documents"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"documents"===l?"bg-insurance-blue text-white":"text-gray-600 hover:text-gray-900"}`,children:["\uD83D\uDCC1 Dokumenter (",e.length,")"]})]}),"dashboard"===l&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-8 text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"AI Insights Dashboard"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Avanceret dashboard med proaktive AI-indsigter kommer snart. Kr\xe6ver Supabase setup og brugerautentificering."}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDEE1️"}),(0,t.jsx)("h3",{className:"font-semibold",children:"Coverage Analysis"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Analyse af d\xe6kningshuller"})]}),(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCB0"}),(0,t.jsx)("h3",{className:"font-semibold",children:"Cost Optimization"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Besparelsesmuligheder"})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCC5"}),(0,t.jsx)("h3",{className:"font-semibold",children:"Renewal Reminders"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"P\xe5mindelser om fornyelse"})]})]})]}),"documents"===l&&(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Upload Nyt Dokument"}),(0,t.jsx)(h,{onDocumentParsed:d})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Dine Dokumenter"}),(0,t.jsx)(g,{documents:e})]})]}),"chat"===l&&(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-300px)]",children:[(0,t.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Hurtig Upload"}),(0,t.jsx)(h,{onDocumentParsed:d})]}),e.length>0&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Seneste Dokumenter"}),(0,t.jsx)("div",{className:"space-y-2",children:e.slice(0,3).map((e,s)=>(0,t.jsxs)("button",{onClick:()=>n(e),className:"w-full text-left p-2 rounded border hover:bg-gray-50 text-sm",children:[(0,t.jsx)("div",{className:"font-medium truncate",children:e.name}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:e.type})]},s))})]})]}),(0,t.jsx)("div",{className:"lg:col-span-3 flex flex-col",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border flex-1 flex flex-col",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"AI Forsikringsr\xe5dgiver"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Still sp\xf8rgsm\xe5l om forsikring eller upload dokumenter til analyse"})]}),(0,t.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,t.jsx)(u,{})})]})})]})]})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9138:(e,s,r)=>{Promise.resolve().then(r.bind(r,6859))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,945,607],()=>r(1306));module.exports=t})();