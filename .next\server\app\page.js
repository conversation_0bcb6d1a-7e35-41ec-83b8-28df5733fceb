(()=>{var e={};e.id=974,e.ids=[974],e.modules={682:(e,s,t)=>{Promise.resolve().then(t.bind(t,1204))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI forsikrings guiden\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx","default")},1306:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(5239),n=t(8088),a=t(8170),i=t.n(a),l=t(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1204)),"C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Projects\\AI forsikrings guiden\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1605:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3461:()=>{},3873:e=>{"use strict";e.exports=require("path")},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var r=t(7413);t(1135);var n=t(5041),a=t.n(n);let i={title:"AI Forsikringsguiden",description:"Digital AI-assistent til forsikringsrelaterede henvendelser og analyser"};function l({children:e}){return(0,r.jsx)("html",{lang:"da",children:(0,r.jsx)("body",{className:a().className,children:(0,r.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-insurance-blue",children:"AI Forsikringsguiden"})}),(0,r.jsxs)("nav",{className:"flex space-x-4",children:[(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-insurance-blue",children:"Chat"}),(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-insurance-blue",children:"Mine Dokumenter"}),(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-insurance-blue",children:"Hj\xe6lp"})]})]})})}),(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)("footer",{className:"bg-gray-100 border-t",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,r.jsx)("p",{className:"text-sm text-gray-600 text-center",children:"AI Forsikringsguiden - Ikke juridisk r\xe5dgivning. Konsulter altid en advokat ved komplekse sager."})})})]})})})}},5891:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(687),n=t(3210);function a({onSendMessage:e}){let[s,t]=(0,n.useState)(""),[a,i]=(0,n.useState)(!1),l=async r=>{r.preventDefault(),s.trim()&&!a&&(i(!0),await e(s.trim()),t(""),i(!1))};return(0,r.jsxs)("form",{onSubmit:l,className:"flex space-x-2",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("textarea",{value:s,onChange:e=>t(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),l(e))},placeholder:"Skriv dit forsikringssp\xf8rgsm\xe5l her...",className:"input-field resize-none h-12",rows:1,disabled:a})}),(0,r.jsx)("button",{type:"submit",disabled:!s.trim()||a,className:"btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:a?(0,r.jsxs)("svg",{className:"animate-spin h-5 w-5",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):"Send"})]})}function i(){let[e,s]=(0,n.useState)([{id:"1",content:"Hej! Jeg er din AI forsikringsassistent. Hvordan kan jeg hj\xe6lpe dig i dag?",role:"assistant",timestamp:new Date}]),t=async t=>{let r={id:Date.now().toString(),content:t,role:"user",timestamp:new Date};s(e=>[...e,r]);try{let t=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...e,r].map(e=>({role:e.role,content:e.content})),userDocuments:[]})});if(!t.ok)throw Error("Failed to get AI response");let n=await t.json(),a={id:(Date.now()+1).toString(),content:n.message,role:"assistant",timestamp:new Date};s(e=>[...e,a])}catch(t){console.error("Error sending message:",t);let e={id:(Date.now()+1).toString(),content:"Beklager, der opstod en fejl. Pr\xf8v venligst igen senere.",role:"assistant",timestamp:new Date};s(s=>[...s,e])}};return(0,r.jsxs)("div",{className:"card max-w-4xl mx-auto",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Chat med AI Forsikringsassistent"}),(0,r.jsx)("div",{className:"h-96 overflow-y-auto mb-4 border rounded-lg p-4 bg-gray-50",children:e.map(e=>(0,r.jsx)("div",{className:`mb-4 ${"user"===e.role?"text-right":"text-left"}`,children:(0,r.jsxs)("div",{className:`inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${"user"===e.role?"bg-insurance-blue text-white":"bg-white text-gray-900 border"}`,children:[(0,r.jsx)("p",{className:"text-sm",children:e.content}),(0,r.jsx)("p",{className:"text-xs opacity-70 mt-1",children:e.timestamp.toLocaleTimeString("da-DK",{hour:"2-digit",minute:"2-digit"})})]})},e.id))}),(0,r.jsx)(a,{onSendMessage:t})]})}function l({onDocumentParsed:e}){let[s,t]=(0,n.useState)(!1),[a,i]=(0,n.useState)(null),l=async s=>{let r=s.target.files?.[0];if(r){t(!0),i(null);try{let s="";if("application/pdf"===r.type)s=`[PDF Dokument: ${r.name}]
Dette er en PDF fil. Indhold vil blive behandlet n\xe5r backend PDF parsing er implementeret.`;else if("text/plain"===r.type)s=await r.text();else if(r.type.startsWith("image/"))s=`[Billede: ${r.name}]
Dette er et billede. OCR behandling vil blive implementeret for at udtr\xe6kke tekst.`;else throw Error("Ikke-underst\xf8ttet filtype. Upload venligst PDF, tekst eller billede filer.");let t=o(r.name,s);e({name:r.name,content:s,type:t})}catch(e){console.error("Upload error:",e),i(e instanceof Error?e.message:"Der opstod en fejl ved upload")}finally{t(!1),s.target.value=""}}},o=(e,s)=>{let t=e.toLowerCase()+" "+s.toLowerCase();return t.includes("police")||t.includes("forsikring")||t.includes("d\xe6kning")?"insurance_policy":t.includes("skade")||t.includes("anmeldelse")||t.includes("erstatning")?"claim_document":t.includes("korrespondance")||t.includes("brev")||t.includes("mail")?"correspondence":"general_document"};return(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-insurance-blue transition-colors",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"mx-auto w-12 h-12 text-gray-400",children:(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 48 48",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"})})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"file-upload",className:"cursor-pointer",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-insurance-blue hover:text-insurance-dark",children:"Upload forsikringsdokument"}),(0,r.jsx)("input",{id:"file-upload",name:"file-upload",type:"file",className:"sr-only",accept:".pdf,.txt,.jpg,.jpeg,.png",onChange:l,disabled:s})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"PDF, tekst eller billede filer op til 10MB"})]}),s&&(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsxs)("svg",{className:"animate-spin h-4 w-4 text-insurance-blue",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Behandler dokument..."})]}),a&&(0,r.jsx)("div",{className:"text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2",children:a})]})})}function o({documents:e,onDocumentSelect:s,highlightTerms:t=[]}){let[a,i]=(0,n.useState)(e.length>0?e[0]:null),[l,o]=(0,n.useState)(""),d=e=>{i(e),s?.(e)},c=e=>{switch(e){case"insurance_policy":return"\uD83D\uDCCB";case"claim_document":return"\uD83D\uDD27";case"correspondence":return"✉️";default:return"\uD83D\uDCC4"}},m=e=>{switch(e){case"insurance_policy":return"Forsikringspolice";case"claim_document":return"Skadeanmeldelse";case"correspondence":return"Korrespondance";default:return"Dokument"}};return 0===e.length?(0,r.jsxs)("div",{className:"card text-center py-8",children:[(0,r.jsx)("div",{className:"text-gray-400 text-4xl mb-4",children:"\uD83D\uDCC4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Ingen dokumenter uploadet"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Upload forsikringsdokumenter for at f\xe5 personaliseret hj\xe6lp"})]}):(0,r.jsx)("div",{className:"card",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,r.jsxs)("div",{className:"lg:w-1/3",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:["Mine Dokumenter (",e.length,")"]}),(0,r.jsx)("div",{className:"space-y-2",children:e.map((e,s)=>(0,r.jsx)("button",{onClick:()=>d(e),className:`w-full text-left p-3 rounded-lg border transition-colors ${a?.name===e.name?"border-insurance-blue bg-insurance-light":"border-gray-200 hover:border-gray-300"}`,children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("span",{className:"text-lg",children:c(e.type)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:m(e.type)})]})]})},s))})]}),(0,r.jsx)("div",{className:"lg:w-2/3",children:a&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:a.name}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("input",{type:"text",placeholder:"S\xf8g i dokument...",value:l,onChange:e=>o(e.target.value),className:"text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-1 focus:ring-insurance-blue focus:border-insurance-blue"})})]}),(0,r.jsx)("div",{className:"bg-gray-50 border rounded-lg p-4 max-h-96 overflow-y-auto",children:(0,r.jsx)("div",{className:"text-sm text-gray-700 whitespace-pre-wrap leading-relaxed",dangerouslySetInnerHTML:{__html:((e,s)=>{if(!s)return e;let t=RegExp(`(${s})`,"gi");return e.replace(t,'<mark class="bg-blue-200 px-1 rounded font-semibold">$1</mark>')})(((e,s)=>{if(0===s.length)return e;let t=e;return s.forEach(e=>{let s=RegExp(`(${e})`,"gi");t=t.replace(s,'<mark class="bg-yellow-200 px-1 rounded">$1</mark>')}),t})(a.content,t),l)}})}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between text-xs text-gray-500",children:[(0,r.jsxs)("span",{children:["Type: ",m(a.type)]}),a.uploadDate&&(0,r.jsxs)("span",{children:["Uploadet: ",a.uploadDate.toLocaleDateString("da-DK")]})]})]})})]})})}function d(){let[e,s]=(0,n.useState)([]);return(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Velkommen til AI Forsikringsguiden"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:"F\xe5 hj\xe6lp til forsikringssp\xf8rgsm\xe5l, analyse af policer og juridisk vejledning fra din AI-assistent."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Forsikringsr\xe5dgivning"}),(0,r.jsx)("p",{className:"text-gray-600",children:"F\xe5 hj\xe6lp til at v\xe6lge den rigtige forsikring og forst\xe5 dine muligheder."})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Dokumentanalyse"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Upload dine forsikringspapirer og f\xe5 dem forklaret p\xe5 dansk."})]}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Juridisk Vejledning"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Forst\xe5 dine rettigheder og f\xe5 guidance i forsikringstvister."})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Upload Dokumenter"}),(0,r.jsx)(l,{onDocumentParsed:e=>{let t={...e,uploadDate:new Date};s(e=>[...e,t])}})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Mine Dokumenter"}),(0,r.jsx)(o,{documents:e})]})]}),(0,r.jsx)(i,{})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9138:(e,s,t)=>{Promise.resolve().then(t.bind(t,5891))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9679:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},9927:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,945],()=>t(1306));module.exports=r})();