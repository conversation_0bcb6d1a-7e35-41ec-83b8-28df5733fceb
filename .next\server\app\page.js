(()=>{var e={};e.id=974,e.ids=[974],e.modules={538:(e,t,r)=>{"use strict";var n=r(8354),s=r(7802);e.exports=function(e){if("number"==typeof e)return e;var t=s(e);return void 0===t&&console.warn(Error(n.format("humanize-ms(%j) result undefined",e)).stack),t}},682:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},907:(e,t,r)=>{"use strict";var n=r(3210),s=r(7379),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=s.useSyncExternalStore,a=n.useRef,l=n.useEffect,c=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,s){var d=a(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;var f=o(e,(d=c(function(){function e(e){if(!l){if(l=!0,o=e,e=n(e),void 0!==s&&h.hasValue){var t=h.value;if(s(t,e))return a=t}return a=e}if(t=a,i(o,e))return t;var r=n(e);return void 0!==s&&s(t,r)?(o=e,t):(o=e,a=r)}var o,a,l=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,s]))[0],d[1]);return l(function(){h.hasValue=!0,h.value=f},[f]),u(f),f}},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI forsikrings guiden\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx","default")},1306:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>h,tree:()=>c});var n=r(5239),s=r(8088),i=r(8170),o=r.n(i),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Projects\\AI forsikrings guiden\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}],u=["C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2367:(e,t,r)=>{"use strict";let n,s,i,o,a,l,c,u,d,h,f;r.r(t),r.d(t,{default:()=>ib});var p,m,g,y,b,_,v,w,x,S,k,T,j,E,C,P,R,A,O,N,I,$,q,D,L,B,M,F,W,z,U,H,V,J,K,X,G,Y,Q,Z,ee,et,er,en,es,ei,eo,ea,el,ec,eu,ed,eh,ef,ep,em,eg,ey,eb,e_,ev,ew,ex,eS,ek,eT,ej,eE,eC,eP,eR,eA,eO,eN,eI,e$,eq,eD,eL=r(687),eB=r(3210),eM=r(1219),eF=r(9481);let eW=[{id:"basic_functionality",type:"data_processing",title:"Grundl\xe6ggende funktionalitet",description:"N\xf8dvendig for at bruge AI Forsikringsguiden. Inkluderer chat, dokumentupload og brugerkontooprettelse.",required:!0,purpose:"Levering af grundl\xe6ggende tjenester",legalBasis:"GDPR Artikel 6(1)(b) - Kontraktuel forpligtelse"},{id:"chat_history",type:"data_processing",title:"Samtalehistorik",description:"Gem dine samtaler for at give bedre, kontekstuel r\xe5dgivning over tid.",required:!1,purpose:"Forbedret brugeroplevelse og kontinuerlig r\xe5dgivning",legalBasis:"GDPR Artikel 6(1)(a) - Samtykke"},{id:"document_storage",type:"document_storage",title:"Dokumentlagring",description:"Opbevar dine uploadede forsikringsdokumenter sikkert for fremtidig reference og analyse.",required:!1,purpose:"Personaliseret dokumentanalyse og r\xe5dgivning",legalBasis:"GDPR Artikel 6(1)(a) - Samtykke"},{id:"ai_training",type:"ai_training",title:"AI-forbedring (anonymiseret)",description:"Brug anonymiserede data til at forbedre AI-systemets evne til at hj\xe6lpe danske forsikringstagere.",required:!1,purpose:"Forskning og udvikling af AI-systemer",legalBasis:"GDPR Artikel 6(1)(a) - Samtykke"},{id:"analytics",type:"analytics",title:"Anonyme brugsstatistikker",description:"Indsaml anonyme data om hvordan tjenesten bruges for at forbedre funktionalitet.",required:!1,purpose:"Produktforbedring og brugeroplevelse",legalBasis:"GDPR Artikel 6(1)(f) - Legitime interesser"},{id:"marketing",type:"marketing",title:"Produktopdateringer",description:"Modtag e-mails om nye funktioner, opdateringer og relevant forsikringsinformation.",required:!1,purpose:"Informative kommunikation om tjenesten",legalBasis:"GDPR Artikel 6(1)(a) - Samtykke"}];function ez({isOpen:e,onClose:t,onComplete:r}){let[n,s]=(0,eB.useState)(()=>{let e={};return eW.forEach(t=>{e[t.id]=t.required}),e}),[i,o]=(0,eB.useState)(!1),[a,l]=(0,eB.useState)(0),{user:c}=(0,eM.A)(),u=(0,eF.UU)(),d=["Velkommen","Databehandling","Dine rettigheder","Samtykker"];if(!e)return null;let h=(e,t)=>{s(r=>({...r,[e]:t}))},f=async()=>{if(!c)return void console.error("No user logged in");o(!0);try{let e=eW.map(e=>({user_id:c.id,consent_type:e.type,consent_given:n[e.id],consent_version:"1.0",purpose:e.purpose,legal_basis:e.legalBasis,metadata:{option_id:e.id,title:e.title,required:e.required}})),{error:s}=await u.from("user_consents").insert(e);if(s)return void console.error("Error saving consents:",s);r(n),t()}catch(e){console.error("Error submitting consents:",e)}finally{o(!1)}};return(0,eL.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,eL.jsx)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,eL.jsxs)("div",{className:"p-6",children:[(0,eL.jsxs)("div",{className:"mb-6",children:[(0,eL.jsx)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:d.map((e,t)=>(0,eL.jsx)("span",{className:t<=a?"text-insurance-blue font-medium":"",children:e},e))}),(0,eL.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,eL.jsx)("div",{className:"bg-insurance-blue h-2 rounded-full transition-all duration-300",style:{width:`${(a+1)/d.length*100}%`}})})]}),(0,eL.jsxs)("div",{className:"min-h-[400px]",children:[0===a&&(0,eL.jsx)(()=>(0,eL.jsxs)("div",{className:"text-center",children:[(0,eL.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Velkommen til AI Forsikringsguiden"}),(0,eL.jsxs)("div",{className:"text-gray-600 space-y-4",children:[(0,eL.jsx)("p",{children:"Vi tager dit privatliv alvorligt. Denne guide forklarer hvordan vi behandler dine data og giver dig kontrol over dine oplysninger."}),(0,eL.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,eL.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Hvad er AI Forsikringsguiden?"}),(0,eL.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,eL.jsx)("li",{children:"• AI-drevet r\xe5dgivning om forsikringssp\xf8rgsm\xe5l"}),(0,eL.jsx)("li",{children:"• Analyse af dine forsikringsdokumenter"}),(0,eL.jsx)("li",{children:"• Juridisk vejledning baseret p\xe5 dansk lovgivning"}),(0,eL.jsx)("li",{children:"• Personaliseret hj\xe6lp til skadeanmeldelser"})]})]})]})]}),{}),1===a&&(0,eL.jsx)(()=>(0,eL.jsxs)("div",{children:[(0,eL.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Hvordan behandler vi dine data?"}),(0,eL.jsxs)("div",{className:"space-y-6",children:[(0,eL.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,eL.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"\uD83D\uDCCA Hvilke data indsamles?"}),(0,eL.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,eL.jsx)("li",{children:"• Sp\xf8rgsm\xe5l og svar i chatten"}),(0,eL.jsx)("li",{children:"• Uploadede forsikringsdokumenter (med dit samtykke)"}),(0,eL.jsx)("li",{children:"• Brugerkontooplysninger (navn, e-mail)"}),(0,eL.jsx)("li",{children:"• Anonyme brugsstatistikker"})]})]}),(0,eL.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,eL.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"\uD83D\uDD12 Hvordan sikres dine data?"}),(0,eL.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,eL.jsx)("li",{children:"• End-to-end kryptering af sensitive dokumenter"}),(0,eL.jsx)("li",{children:"• Sikre danske servere (Supabase)"}),(0,eL.jsx)("li",{children:"• Ingen deling med tredjepart uden samtykke"}),(0,eL.jsx)("li",{children:"• Regular sikkerhedsaudit"})]})]}),(0,eL.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,eL.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"⏰ Hvor l\xe6nge opbevares data?"}),(0,eL.jsxs)("ul",{className:"text-gray-600 text-sm space-y-1",children:[(0,eL.jsx)("li",{children:"• Chatbeskeder: 2 \xe5r (eller indtil du sletter dem)"}),(0,eL.jsx)("li",{children:"• Dokumenter: Indtil du sletter dem"}),(0,eL.jsx)("li",{children:"• Anonyme statistikker: Permanent (kan ikke spores tilbage)"}),(0,eL.jsx)("li",{children:"• Konto: Indtil du lukker den"})]})]})]})]}),{}),2===a&&(0,eL.jsx)(()=>(0,eL.jsxs)("div",{children:[(0,eL.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Dine rettigheder under GDPR"}),(0,eL.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,eL.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,eL.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"\uD83D\uDCE5 Ret til indsigt"}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm",children:"Du kan til enhver tid f\xe5 en kopi af alle dine data i et struktureret format."})]}),(0,eL.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,eL.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"✏️ Ret til rettelse"}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm",children:"Du kan rette forkerte oplysninger i din profil og dine data."})]}),(0,eL.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,eL.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"\uD83D\uDDD1️ Ret til sletning"}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm",children:"Du kan slette dine data og lukke din konto n\xe5r som helst."})]}),(0,eL.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,eL.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"\uD83D\uDCE6 Ret til dataportabilitet"}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm",children:"Du kan eksportere dine data til brug i andre tjenester."})]}),(0,eL.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,eL.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"❌ Ret til indsigelse"}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm",children:"Du kan til enhver tid tr\xe6kke dit samtykke tilbage."})]}),(0,eL.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,eL.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"⚖️ Ret til klage"}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm",children:"Du kan klage til Datatilsynet hvis du er utilfreds."})]})]})]}),{}),3===a&&(0,eL.jsx)(()=>(0,eL.jsxs)("div",{children:[(0,eL.jsx)("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"V\xe6lg dine samtykker"}),(0,eL.jsx)("p",{className:"text-gray-600 mb-6",children:"Du kan til enhver tid \xe6ndre disse indstillinger i din profil."}),(0,eL.jsx)("div",{className:"space-y-4",children:eW.map(e=>(0,eL.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,eL.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,eL.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,eL.jsx)("input",{type:"checkbox",id:e.id,checked:n[e.id],onChange:t=>h(e.id,t.target.checked),disabled:e.required,className:"h-4 w-4 text-insurance-blue focus:ring-insurance-blue border-gray-300 rounded"})}),(0,eL.jsx)("div",{className:"flex-1",children:(0,eL.jsxs)("label",{htmlFor:e.id,className:"block",children:[(0,eL.jsxs)("div",{className:"font-medium text-gray-900 mb-1",children:[e.title,e.required&&(0,eL.jsx)("span",{className:"ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded",children:"P\xe5kr\xe6vet"})]}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm mb-2",children:e.description}),(0,eL.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,eL.jsx)("strong",{children:"Form\xe5l:"})," ",e.purpose,(0,eL.jsx)("br",{}),(0,eL.jsx)("strong",{children:"Retsgrundlag:"})," ",e.legalBasis]})]})})]})},e.id))})]}),{})]}),(0,eL.jsxs)("div",{className:"flex items-center justify-between mt-6 pt-6 border-t",children:[(0,eL.jsx)("button",{onClick:()=>l(Math.max(0,a-1)),disabled:0===a,className:"btn-secondary disabled:opacity-50 disabled:cursor-not-allowed",children:"Tilbage"}),a<d.length-1?(0,eL.jsx)("button",{onClick:()=>l(a+1),className:"btn-primary",children:"N\xe6ste"}):(0,eL.jsx)("button",{onClick:f,disabled:!eW.filter(e=>e.required).every(e=>n[e.id])||i,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:i?"Gemmer...":"Accepter og forts\xe6t"})]}),(0,eL.jsxs)("div",{className:"mt-6 pt-4 border-t text-center text-sm text-gray-500",children:[(0,eL.jsxs)("p",{children:["Har du sp\xf8rgsm\xe5l? Kontakt os p\xe5"," ",(0,eL.jsx)("a",{href:"mailto:<EMAIL>",className:"text-insurance-blue hover:underline",children:"<EMAIL>"})]}),(0,eL.jsxs)("p",{className:"mt-2",children:[(0,eL.jsx)("a",{href:"/privacy-policy",className:"text-insurance-blue hover:underline mr-4",children:"Privatlivspolitik"}),(0,eL.jsx)("a",{href:"/terms",className:"text-insurance-blue hover:underline",children:"Servicevilk\xe5r"})]})]})]})})})}function eU({onSendMessage:e}){let[t,r]=(0,eB.useState)(""),[n,s]=(0,eB.useState)(!1),i=async i=>{i.preventDefault(),t.trim()&&!n&&(s(!0),await e(t.trim()),r(""),s(!1))};return(0,eL.jsxs)("form",{onSubmit:i,className:"flex space-x-2",children:[(0,eL.jsx)("div",{className:"flex-1",children:(0,eL.jsx)("textarea",{value:t,onChange:e=>r(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),i(e))},placeholder:"Skriv dit forsikringssp\xf8rgsm\xe5l her...",className:"input-field resize-none h-12",rows:1,disabled:n})}),(0,eL.jsx)("button",{type:"submit",disabled:!t.trim()||n,className:"btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:n?(0,eL.jsxs)("svg",{className:"animate-spin h-5 w-5",viewBox:"0 0 24 24",children:[(0,eL.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,eL.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):"Send"})]})}function eH(){let[e,t]=(0,eB.useState)([{id:"1",content:"Hej! Jeg er din AI forsikringsassistent. Hvordan kan jeg hj\xe6lpe dig i dag?",role:"assistant",timestamp:new Date}]),r=async r=>{let n={id:Date.now().toString(),content:r,role:"user",timestamp:new Date};t(e=>[...e,n]);try{let r=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...e,n].map(e=>({role:e.role,content:e.content})),userDocuments:[]})});if(!r.ok)throw Error("Failed to get AI response");let s=await r.json(),i={id:(Date.now()+1).toString(),content:s.message,role:"assistant",timestamp:new Date};t(e=>[...e,i])}catch(r){console.error("Error sending message:",r);let e={id:(Date.now()+1).toString(),content:"Beklager, der opstod en fejl. Pr\xf8v venligst igen senere.",role:"assistant",timestamp:new Date};t(t=>[...t,e])}};return(0,eL.jsxs)("div",{className:"card max-w-4xl mx-auto",children:[(0,eL.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Chat med AI Forsikringsassistent"}),(0,eL.jsx)("div",{className:"h-96 overflow-y-auto mb-4 border rounded-lg p-4 bg-gray-50",children:e.map(e=>(0,eL.jsx)("div",{className:`mb-4 ${"user"===e.role?"text-right":"text-left"}`,children:(0,eL.jsxs)("div",{className:`inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${"user"===e.role?"bg-insurance-blue text-white":"bg-white text-gray-900 border"}`,children:[(0,eL.jsx)("p",{className:"text-sm",children:e.content}),(0,eL.jsx)("p",{className:"text-xs opacity-70 mt-1",children:e.timestamp.toLocaleTimeString("da-DK",{hour:"2-digit",minute:"2-digit"})})]})},e.id))}),(0,eL.jsx)(eU,{onSendMessage:r})]})}let eV=e=>{let t,r=new Set,n=(e,n)=>{let s="function"==typeof e?e(t):e;if(!Object.is(s,t)){let e=t;t=(null!=n?n:"object"!=typeof s||null===s)?s:Object.assign({},t,s),r.forEach(r=>r(t,e))}},s=()=>t,i={setState:n,getState:s,getInitialState:()=>o,subscribe:e=>(r.add(e),()=>r.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),r.clear()}},o=t=e(n,s,i);return i},eJ=e=>e?eV(e):eV;var eK=r(9733);let{useDebugValue:eX}=eB,{useSyncExternalStoreWithSelector:eG}=eK,eY=!1,eQ=e=>e,eZ=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let t="function"==typeof e?eJ(e):e,r=(e,r)=>(function(e,t=eQ,r){r&&!eY&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),eY=!0);let n=eG(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,r);return eX(n),n})(t,e,r);return Object.assign(r,t),r};function e0(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var n;let s=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),i=null!=(n=r.getItem(e))?n:null;return i instanceof Promise?i.then(s):s(i)},setItem:(e,n)=>r.setItem(e,JSON.stringify(n,null==t?void 0:t.replacer)),removeItem:e=>r.removeItem(e)}}let e1=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>e1(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>e1(t)(e)}}},e2=(e,t)=>(r,n,s)=>{let i,o,a={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,c=new Set,u=new Set;try{i=a.getStorage()}catch(e){}if(!i)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),r(...e)},n,s);let d=e1(a.serialize),h=()=>{let e,t=d({state:a.partialize({...n()}),version:a.version}).then(e=>i.setItem(a.name,e)).catch(t=>{e=t});if(e)throw e;return t},f=s.setState;s.setState=(e,t)=>{f(e,t),h()};let p=e((...e)=>{r(...e),h()},n,s),m=()=>{var e;if(!i)return;l=!1,c.forEach(e=>e(n()));let t=(null==(e=a.onRehydrateStorage)?void 0:e.call(a,n()))||void 0;return e1(i.getItem.bind(i))(a.name).then(e=>{if(e)return a.deserialize(e)}).then(e=>{if(e)if("number"!=typeof e.version||e.version===a.version)return e.state;else{if(a.migrate)return a.migrate(e.state,e.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}}).then(e=>{var t;return r(o=a.merge(e,null!=(t=n())?t:p),!0),h()}).then(()=>{null==t||t(o,void 0),l=!0,u.forEach(e=>e(o))}).catch(e=>{null==t||t(void 0,e)})};return s.persist={setOptions:e=>{a={...a,...e},e.getStorage&&(i=e.getStorage())},clearStorage:()=>{null==i||i.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>m(),hasHydrated:()=>l,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},m(),o||p},e3=(e,t)=>(r,n,s)=>{let i,o={storage:e0(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},a=!1,l=new Set,c=new Set,u=o.storage;if(!u)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),r(...e)},n,s);let d=()=>{let e=o.partialize({...n()});return u.setItem(o.name,{state:e,version:o.version})},h=s.setState;s.setState=(e,t)=>{h(e,t),d()};let f=e((...e)=>{r(...e),d()},n,s);s.getInitialState=()=>f;let p=()=>{var e,t;if(!u)return;a=!1,l.forEach(e=>{var t;return e(null!=(t=n())?t:f)});let s=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=n())?e:f))||void 0;return e1(u.getItem.bind(u))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate)return[!0,o.migrate(e.state,e.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[s,a]=e;if(r(i=o.merge(a,null!=(t=n())?t:f),!0),s)return d()}).then(()=>{null==s||s(i,void 0),i=n(),a=!0,c.forEach(e=>e(i))}).catch(e=>{null==s||s(void 0,e)})};return s.persist={setOptions:e=>{o={...o,...e},e.storage&&(u=e.storage)},clearStorage:()=>{null==u||u.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>p(),hasHydrated:()=>a,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},o.skipHydration||p(),i||f},e4=(e=>e?eZ(e):eZ)()(((e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?(console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),e2(e,t)):e3(e,t))((e,t)=>({user:null,profile:null,documents:[],selectedDocument:null,conversations:[],currentConversation:null,messages:[],policies:[],claims:[],insights:[],isLoading:!1,isChatLoading:!1,error:null,settings:{theme:"light",language:"da",notifications:{email:!0,push:!0,reminders:!0},privacy:{chatHistory:!1,documentStorage:!1,aiTraining:!1,analytics:!1,marketing:!1}},setUser:t=>e({user:t}),setProfile:t=>e({profile:t}),setDocuments:t=>e({documents:t}),addDocument:t=>e(e=>({documents:[t,...e.documents]})),removeDocument:t=>e(e=>({documents:e.documents.filter(e=>e.id!==t),selectedDocument:e.selectedDocument?.id===t?null:e.selectedDocument})),selectDocument:t=>e({selectedDocument:t}),setConversations:t=>e({conversations:t}),setCurrentConversation:t=>e({currentConversation:t,messages:[]}),setMessages:t=>e({messages:t}),addMessage:t=>e(e=>({messages:[...e.messages,t]})),updateMessage:(t,r)=>e(e=>({messages:e.messages.map(e=>e.id===t?{...e,...r}:e)})),setPolicies:t=>e({policies:t}),addPolicy:t=>e(e=>({policies:[t,...e.policies]})),updatePolicy:(t,r)=>e(e=>({policies:e.policies.map(e=>e.id===t?{...e,...r}:e)})),setClaims:t=>e({claims:t}),addClaim:t=>e(e=>({claims:[t,...e.claims]})),updateClaim:(t,r)=>e(e=>({claims:e.claims.map(e=>e.id===t?{...e,...r}:e)})),setInsights:t=>e({insights:t}),addInsight:t=>e(e=>({insights:[t,...e.insights]})),acknowledgeInsight:t=>e(e=>({insights:e.insights.map(e=>e.id===t?{...e,is_acknowledged:!0,acknowledged_at:new Date().toISOString()}:e)})),setLoading:t=>e({isLoading:t}),setChatLoading:t=>e({isChatLoading:t}),setError:t=>e({error:t}),updateSettings:t=>e(e=>({settings:{...e.settings,...t}})),updatePrivacySettings:t=>e(e=>({settings:{...e.settings,privacy:{...e.settings.privacy,...t}}})),initializeUser:t=>e({user:t.user,profile:t.profile,documents:t.documents||[],conversations:t.conversations||[],policies:t.policies||[],claims:t.claims||[],insights:t.insights||[],error:null}),clearUserData:()=>e({user:null,profile:null,documents:[],selectedDocument:null,conversations:[],currentConversation:null,messages:[],policies:[],claims:[],insights:[],error:null}),getDocumentsByType:e=>t().documents.filter(t=>t.document_type===e),getPoliciesByStatus:e=>t().policies.filter(t=>t.status===e),getUnacknowledgedInsights:()=>t().insights.filter(e=>!e.is_acknowledged),getTotalCoverage:()=>t().policies.filter(e=>"active"===e.status&&e.coverage_amount).reduce((e,t)=>e+(t.coverage_amount||0),0),getTotalPremiums:()=>t().policies.filter(e=>"active"===e.status&&e.premium_amount).reduce((e,t)=>e+(t.premium_amount||0),0)}),{name:"insurance-store",storage:e0(()=>({getItem:e=>{let t=localStorage.getItem(e);return t?JSON.parse(t):null},setItem:(e,t)=>{localStorage.setItem(e,JSON.stringify(t))},removeItem:e=>{localStorage.removeItem(e)}})),partialize:e=>({settings:e.settings,selectedDocument:e.selectedDocument?.id?{id:e.selectedDocument.id}:null,currentConversation:e.currentConversation?.id?{id:e.currentConversation.id}:null})})),e6="RFC3986",e8={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},e5=(Object.prototype.hasOwnProperty,Array.isArray),e9=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function e7(e,t){if(e5(e)){let r=[];for(let n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)}let te=Object.prototype.hasOwnProperty,tt={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},tr=Array.isArray,tn=Array.prototype.push,ts=function(e,t){tn.apply(e,tr(t)?t:[t])},ti=Date.prototype.toISOString,to={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,r,n,s)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let o="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,r=[];for(let e=0;e<t.length;++e){let n=t.charCodeAt(e);if(45===n||46===n||95===n||126===n||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||"RFC1738"===s&&(40===n||41===n)){r[r.length]=t.charAt(e);continue}if(n<128){r[r.length]=e9[n];continue}if(n<2048){r[r.length]=e9[192|n>>6]+e9[128|63&n];continue}if(n<55296||n>=57344){r[r.length]=e9[224|n>>12]+e9[128|n>>6&63]+e9[128|63&n];continue}e+=1,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),r[r.length]=e9[240|n>>18]+e9[128|n>>12&63]+e9[128|n>>6&63]+e9[128|63&n]}o+=r.join("")}return o},encodeValuesOnly:!1,format:e6,formatter:e8[e6],indices:!1,serializeDate:e=>ti.call(e),skipNulls:!1,strictNullHandling:!1},ta={},tl="4.104.0",tc=!1;var tu=r(7910),td=r(1630),th=r(9551),tf=r(7192),tp=r(5591),tm=r(4075);let tg=tu.Readable,ty=Symbol("buffer"),tb=Symbol("type");class t_{constructor(){this[tb]="";let e=arguments[0],t=arguments[1],r=[];if(e){let t=Number(e.length);for(let n=0;n<t;n++){let t,s=e[n];(t=s instanceof Buffer?s:ArrayBuffer.isView(s)?Buffer.from(s.buffer,s.byteOffset,s.byteLength):s instanceof ArrayBuffer?Buffer.from(s):s instanceof t_?s[ty]:Buffer.from("string"==typeof s?s:String(s))).length,r.push(t)}}this[ty]=Buffer.concat(r);let n=t&&void 0!==t.type&&String(t.type).toLowerCase();n&&!/[^\u0020-\u007E]/.test(n)&&(this[tb]=n)}get size(){return this[ty].length}get type(){return this[tb]}text(){return Promise.resolve(this[ty].toString())}arrayBuffer(){let e=this[ty];return Promise.resolve(e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength))}stream(){let e=new tg;return e._read=function(){},e.push(this[ty]),e.push(null),e}toString(){return"[object Blob]"}slice(){let e,t,r=this.size,n=arguments[0],s=arguments[1];e=void 0===n?0:n<0?Math.max(r+n,0):Math.min(n,r);let i=Math.max((void 0===s?r:s<0?Math.max(r+s,0):Math.min(s,r))-e,0),o=this[ty].slice(e,e+i),a=new t_([],{type:arguments[2]});return a[ty]=o,a}}function tv(e,t,r){Error.call(this,e),this.message=e,this.type=t,r&&(this.code=this.errno=r.code),Error.captureStackTrace(this,this.constructor)}Object.defineProperties(t_.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),Object.defineProperty(t_.prototype,Symbol.toStringTag,{value:"Blob",writable:!1,enumerable:!1,configurable:!0}),tv.prototype=Object.create(Error.prototype),tv.prototype.constructor=tv,tv.prototype.name="FetchError";try{n=require("encoding").convert}catch(e){}let tw=Symbol("Body internals"),tx=tu.PassThrough;function tS(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.size,s=r.timeout;null==e?e=null:tT(e)?e=Buffer.from(e.toString()):tj(e)||Buffer.isBuffer(e)||("[object ArrayBuffer]"===Object.prototype.toString.call(e)?e=Buffer.from(e):ArrayBuffer.isView(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof tu||(e=Buffer.from(String(e)))),this[tw]={body:e,disturbed:!1,error:null},this.size=void 0===n?0:n,this.timeout=void 0===s?0:s,e instanceof tu&&e.on("error",function(e){let r="AbortError"===e.name?e:new tv(`Invalid response body while trying to fetch ${t.url}: ${e.message}`,"system",e);t[tw].error=r})}function tk(){var e=this;if(this[tw].disturbed)return tS.Promise.reject(TypeError(`body used already for: ${this.url}`));if(this[tw].disturbed=!0,this[tw].error)return tS.Promise.reject(this[tw].error);let t=this.body;if(null===t)return tS.Promise.resolve(Buffer.alloc(0));if(tj(t)&&(t=t.stream()),Buffer.isBuffer(t))return tS.Promise.resolve(t);if(!(t instanceof tu))return tS.Promise.resolve(Buffer.alloc(0));let r=[],n=0,s=!1;return new tS.Promise(function(i,o){let a;e.timeout&&(a=setTimeout(function(){s=!0,o(new tv(`Response timeout while trying to fetch ${e.url} (over ${e.timeout}ms)`,"body-timeout"))},e.timeout)),t.on("error",function(t){"AbortError"===t.name?(s=!0,o(t)):o(new tv(`Invalid response body while trying to fetch ${e.url}: ${t.message}`,"system",t))}),t.on("data",function(t){if(!s&&null!==t){if(e.size&&n+t.length>e.size){s=!0,o(new tv(`content size at ${e.url} over limit: ${e.size}`,"max-size"));return}n+=t.length,r.push(t)}}),t.on("end",function(){if(!s){clearTimeout(a);try{i(Buffer.concat(r,n))}catch(t){o(new tv(`Could not create Buffer from response body for ${e.url}: ${t.message}`,"system",t))}}})})}function tT(e){return"object"==typeof e&&"function"==typeof e.append&&"function"==typeof e.delete&&"function"==typeof e.get&&"function"==typeof e.getAll&&"function"==typeof e.has&&"function"==typeof e.set&&("URLSearchParams"===e.constructor.name||"[object URLSearchParams]"===Object.prototype.toString.call(e)||"function"==typeof e.sort)}function tj(e){return"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&"function"==typeof e.constructor&&"string"==typeof e.constructor.name&&/^(Blob|File)$/.test(e.constructor.name)&&/^(Blob|File)$/.test(e[Symbol.toStringTag])}function tE(e){let t,r,n=e.body;if(e.bodyUsed)throw Error("cannot clone body after it is used");return n instanceof tu&&"function"!=typeof n.getBoundary&&(t=new tx,r=new tx,n.pipe(t),n.pipe(r),e[tw].body=t,n=r),n}function tC(e){if(null===e)return null;if("string"==typeof e)return"text/plain;charset=UTF-8";if(tT(e))return"application/x-www-form-urlencoded;charset=UTF-8";if(tj(e))return e.type||null;if(Buffer.isBuffer(e))return null;else if("[object ArrayBuffer]"===Object.prototype.toString.call(e))return null;else if(ArrayBuffer.isView(e))return null;else if("function"==typeof e.getBoundary)return`multipart/form-data;boundary=${e.getBoundary()}`;else if(e instanceof tu)return null;else return"text/plain;charset=UTF-8"}function tP(e){let t=e.body;return null===t?0:tj(t)?t.size:Buffer.isBuffer(t)?t.length:t&&"function"==typeof t.getLengthSync?t._lengthRetrievers&&0==t._lengthRetrievers.length||t.hasKnownLength&&t.hasKnownLength()?t.getLengthSync():null:null}tS.prototype={get body(){return this[tw].body},get bodyUsed(){return this[tw].disturbed},arrayBuffer(){return tk.call(this).then(function(e){return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)})},blob(){let e=this.headers&&this.headers.get("content-type")||"";return tk.call(this).then(function(t){return Object.assign(new t_([],{type:e.toLowerCase()}),{[ty]:t})})},json(){var e=this;return tk.call(this).then(function(t){try{return JSON.parse(t.toString())}catch(t){return tS.Promise.reject(new tv(`invalid json response body at ${e.url} reason: ${t.message}`,"invalid-json"))}})},text(){return tk.call(this).then(function(e){return e.toString()})},buffer(){return tk.call(this)},textConverted(){var e=this;return tk.call(this).then(function(t){return function(e,t){let r,s;if("function"!=typeof n)throw Error("The package `encoding` must be installed to use the textConverted() function");let i=t.get("content-type"),o="utf-8";return i&&(r=/charset=([^;]*)/i.exec(i)),s=e.slice(0,1024).toString(),!r&&s&&(r=/<meta.+?charset=(['"])(.+?)\1/i.exec(s)),!r&&s&&(!(r=/<meta[\s]+?http-equiv=(['"])content-type\1[\s]+?content=(['"])(.+?)\2/i.exec(s))&&(r=/<meta[\s]+?content=(['"])(.+?)\1[\s]+?http-equiv=(['"])content-type\3/i.exec(s))&&r.pop(),r&&(r=/charset=(.*)/i.exec(r.pop()))),!r&&s&&(r=/<\?xml.+?encoding=(['"])(.+?)\1/i.exec(s)),r&&("gb2312"===(o=r.pop())||"gbk"===o)&&(o="gb18030"),n(e,"UTF-8",o).toString()}(t,e.headers)})}},Object.defineProperties(tS.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0}}),tS.mixIn=function(e){for(let t of Object.getOwnPropertyNames(tS.prototype))if(!(t in e)){let r=Object.getOwnPropertyDescriptor(tS.prototype,t);Object.defineProperty(e,t,r)}},tS.Promise=global.Promise;let tR=/[^\^_`a-zA-Z\-0-9!#$%&'*+.|~]/,tA=/[^\t\x20-\x7e\x80-\xff]/;function tO(e){if(e=`${e}`,tR.test(e)||""===e)throw TypeError(`${e} is not a legal HTTP header name`)}function tN(e){if(e=`${e}`,tA.test(e))throw TypeError(`${e} is not a legal HTTP header value`)}function tI(e,t){for(let r in t=t.toLowerCase(),e)if(r.toLowerCase()===t)return r}let t$=Symbol("map");class tq{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;if(this[t$]=Object.create(null),e instanceof tq){let t=e.raw();for(let e of Object.keys(t))for(let r of t[e])this.append(e,r);return}if(null==e);else if("object"==typeof e){let t=e[Symbol.iterator];if(null!=t){if("function"!=typeof t)throw TypeError("Header pairs must be iterable");let r=[];for(let t of e){if("object"!=typeof t||"function"!=typeof t[Symbol.iterator])throw TypeError("Each header pair must be iterable");r.push(Array.from(t))}for(let e of r){if(2!==e.length)throw TypeError("Each header pair must be a name/value tuple");this.append(e[0],e[1])}}else for(let t of Object.keys(e)){let r=e[t];this.append(t,r)}}else throw TypeError("Provided initializer must be an object")}get(e){tO(e=`${e}`);let t=tI(this[t$],e);return void 0===t?null:this[t$][t].join(", ")}forEach(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=tD(this),n=0;for(;n<r.length;){var s=r[n];let i=s[0],o=s[1];e.call(t,o,i,this),r=tD(this),n++}}set(e,t){e=`${e}`,t=`${t}`,tO(e),tN(t);let r=tI(this[t$],e);this[t$][void 0!==r?r:e]=[t]}append(e,t){e=`${e}`,t=`${t}`,tO(e),tN(t);let r=tI(this[t$],e);void 0!==r?this[t$][r].push(t):this[t$][e]=[t]}has(e){return tO(e=`${e}`),void 0!==tI(this[t$],e)}delete(e){tO(e=`${e}`);let t=tI(this[t$],e);void 0!==t&&delete this[t$][t]}raw(){return this[t$]}keys(){return tB(this,"key")}values(){return tB(this,"value")}[Symbol.iterator](){return tB(this,"key+value")}}function tD(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"key+value";return Object.keys(e[t$]).sort().map("key"===t?function(e){return e.toLowerCase()}:"value"===t?function(t){return e[t$][t].join(", ")}:function(t){return[t.toLowerCase(),e[t$][t].join(", ")]})}tq.prototype.entries=tq.prototype[Symbol.iterator],Object.defineProperty(tq.prototype,Symbol.toStringTag,{value:"Headers",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(tq.prototype,{get:{enumerable:!0},forEach:{enumerable:!0},set:{enumerable:!0},append:{enumerable:!0},has:{enumerable:!0},delete:{enumerable:!0},keys:{enumerable:!0},values:{enumerable:!0},entries:{enumerable:!0}});let tL=Symbol("internal");function tB(e,t){let r=Object.create(tM);return r[tL]={target:e,kind:t,index:0},r}let tM=Object.setPrototypeOf({next(){if(!this||Object.getPrototypeOf(this)!==tM)throw TypeError("Value of `this` is not a HeadersIterator");var e=this[tL];let t=e.target,r=e.kind,n=e.index,s=tD(t,r);return n>=s.length?{value:void 0,done:!0}:(this[tL].index=n+1,{value:s[n],done:!1})}},Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));Object.defineProperty(tM,Symbol.toStringTag,{value:"HeadersIterator",writable:!1,enumerable:!1,configurable:!0});let tF=Symbol("Response internals"),tW=td.STATUS_CODES;class tz{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};tS.call(this,e,t);let r=t.status||200,n=new tq(t.headers);if(null!=e&&!n.has("Content-Type")){let t=tC(e);t&&n.append("Content-Type",t)}this[tF]={url:t.url,status:r,statusText:t.statusText||tW[r],headers:n,counter:t.counter}}get url(){return this[tF].url||""}get status(){return this[tF].status}get ok(){return this[tF].status>=200&&this[tF].status<300}get redirected(){return this[tF].counter>0}get statusText(){return this[tF].statusText}get headers(){return this[tF].headers}clone(){return new tz(tE(this),{url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected})}}tS.mixIn(tz.prototype),Object.defineProperties(tz.prototype,{url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}}),Object.defineProperty(tz.prototype,Symbol.toStringTag,{value:"Response",writable:!1,enumerable:!1,configurable:!0});let tU=Symbol("Request internals"),tH=th.URL||tf.URL,tV=th.parse,tJ=th.format;function tK(e){return/^[a-zA-Z][a-zA-Z\d+\-.]*:/.exec(e)&&(e=new tH(e).toString()),tV(e)}let tX="destroy"in tu.Readable.prototype;function tG(e){return"object"==typeof e&&"object"==typeof e[tU]}class tY{constructor(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};tG(e)?t=tK(e.url):(t=e&&e.href?tK(e.href):tK(`${e}`),e={});let n=r.method||e.method||"GET";if(n=n.toUpperCase(),(null!=r.body||tG(e)&&null!==e.body)&&("GET"===n||"HEAD"===n))throw TypeError("Request with GET/HEAD method cannot have body");let s=null!=r.body?r.body:tG(e)&&null!==e.body?tE(e):null;tS.call(this,s,{timeout:r.timeout||e.timeout||0,size:r.size||e.size||0});let i=new tq(r.headers||e.headers||{});if(null!=s&&!i.has("Content-Type")){let e=tC(s);e&&i.append("Content-Type",e)}let o=tG(e)?e.signal:null;if("signal"in r&&(o=r.signal),null!=o&&!function(e){let t=e&&"object"==typeof e&&Object.getPrototypeOf(e);return!!(t&&"AbortSignal"===t.constructor.name)}(o))throw TypeError("Expected signal to be an instanceof AbortSignal");this[tU]={method:n,redirect:r.redirect||e.redirect||"follow",headers:i,parsedURL:t,signal:o},this.follow=void 0!==r.follow?r.follow:void 0!==e.follow?e.follow:20,this.compress=void 0!==r.compress?r.compress:void 0===e.compress||e.compress,this.counter=r.counter||e.counter||0,this.agent=r.agent||e.agent}get method(){return this[tU].method}get url(){return tJ(this[tU].parsedURL)}get headers(){return this[tU].headers}get redirect(){return this[tU].redirect}get signal(){return this[tU].signal}clone(){return new tY(this)}}function tQ(e){Error.call(this,e),this.type="aborted",this.message=e,Error.captureStackTrace(this,this.constructor)}tS.mixIn(tY.prototype),Object.defineProperty(tY.prototype,Symbol.toStringTag,{value:"Request",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(tY.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0}}),tQ.prototype=Object.create(Error.prototype),tQ.prototype.constructor=tQ,tQ.prototype.name="AbortError";let tZ=th.URL||tf.URL,t0=tu.PassThrough,t1=function(e,t){let r=new tZ(t).hostname,n=new tZ(e).hostname;return r===n||"."===r[r.length-n.length-1]&&r.endsWith(n)};function t2(e,t){if(!t2.Promise)throw Error("native promise missing, set fetch.Promise to your favorite alternative");return tS.Promise=t2.Promise,new t2.Promise(function(r,n){var s,i;let o,a,l=new tY(e,t),c=function(e){let t=e[tU].parsedURL,r=new tq(e[tU].headers);if(r.has("Accept")||r.set("Accept","*/*"),!t.protocol||!t.hostname)throw TypeError("Only absolute URLs are supported");if(!/^https?:$/.test(t.protocol))throw TypeError("Only HTTP(S) protocols are supported");if(e.signal&&e.body instanceof tu.Readable&&!tX)throw Error("Cancellation of streamed requests with AbortSignal is not supported in node < 8");let n=null;if(null==e.body&&/^(POST|PUT)$/i.test(e.method)&&(n="0"),null!=e.body){let t=tP(e);"number"==typeof t&&(n=String(t))}n&&r.set("Content-Length",n),r.has("User-Agent")||r.set("User-Agent","node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"),e.compress&&!r.has("Accept-Encoding")&&r.set("Accept-Encoding","gzip,deflate");let s=e.agent;return"function"==typeof s&&(s=s(t)),Object.assign({},t,{method:e.method,headers:function(e){let t=Object.assign({__proto__:null},e[t$]),r=tI(e[t$],"Host");return void 0!==r&&(t[r]=t[r][0]),t}(r),agent:s})}(l),u=("https:"===c.protocol?tp:td).request,d=l.signal,h=null,f=function(){let e=new tQ("The user aborted a request.");n(e),l.body&&l.body instanceof tu.Readable&&t3(l.body,e),h&&h.body&&h.body.emit("error",e)};if(d&&d.aborted)return void f();let p=function(){f(),g()},m=u(c);function g(){m.abort(),d&&d.removeEventListener("abort",p),clearTimeout(o)}d&&d.addEventListener("abort",p),l.timeout&&m.once("socket",function(e){o=setTimeout(function(){n(new tv(`network timeout at: ${l.url}`,"request-timeout")),g()},l.timeout)}),m.on("error",function(e){n(new tv(`request to ${l.url} failed, reason: ${e.message}`,"system",e)),h&&h.body&&t3(h.body,e),g()}),s=m,i=function(e){(!d||!d.aborted)&&h&&h.body&&t3(h.body,e)},s.on("socket",function(e){a=e}),s.on("response",function(e){let t=e.headers;"chunked"!==t["transfer-encoding"]||t["content-length"]||e.once("close",function(e){if(a&&a.listenerCount("data")>0&&!e){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",i(e)}})}),14>parseInt(process.version.substring(1))&&m.on("socket",function(e){e.addListener("close",function(t){let r=e.listenerCount("data")>0;if(h&&r&&!t&&!(d&&d.aborted)){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",h.body.emit("error",e)}})}),m.on("response",function(e){clearTimeout(o);let t=function(e){let t=new tq;for(let r of Object.keys(e))if(!tR.test(r))if(Array.isArray(e[r]))for(let n of e[r])tA.test(n)||(void 0===t[t$][r]?t[t$][r]=[n]:t[t$][r].push(n));else tA.test(e[r])||(t[t$][r]=[e[r]]);return t}(e.headers);if(t2.isRedirect(e.statusCode)){let i=t.get("Location"),o=null;try{o=null===i?null:new tZ(i,l.url).toString()}catch(e){if("manual"!==l.redirect){n(new tv(`uri requested responds with an invalid redirect URL: ${i}`,"invalid-redirect")),g();return}}switch(l.redirect){case"error":n(new tv(`uri requested responds with a redirect, redirect mode is set to error: ${l.url}`,"no-redirect")),g();return;case"manual":if(null!==o)try{t.set("Location",o)}catch(e){n(e)}break;case"follow":var s;if(null===o)break;if(l.counter>=l.follow){n(new tv(`maximum redirect reached at: ${l.url}`,"max-redirect")),g();return}let a={headers:new tq(l.headers),follow:l.follow,counter:l.counter+1,agent:l.agent,compress:l.compress,method:l.method,body:l.body,signal:l.signal,timeout:l.timeout,size:l.size};if(!t1(l.url,o)||(s=l.url,new tZ(o).protocol!==new tZ(s).protocol))for(let e of["authorization","www-authenticate","cookie","cookie2"])a.headers.delete(e);if(303!==e.statusCode&&l.body&&null===tP(l)){n(new tv("Cannot follow redirect with body being a readable stream","unsupported-redirect")),g();return}(303===e.statusCode||(301===e.statusCode||302===e.statusCode)&&"POST"===l.method)&&(a.method="GET",a.body=void 0,a.headers.delete("content-length")),r(t2(new tY(o,a))),g();return}}e.once("end",function(){d&&d.removeEventListener("abort",p)});let i=e.pipe(new t0),a={url:l.url,status:e.statusCode,statusText:e.statusMessage,headers:t,size:l.size,timeout:l.timeout,counter:l.counter},c=t.get("Content-Encoding");if(!l.compress||"HEAD"===l.method||null===c||204===e.statusCode||304===e.statusCode)return void r(h=new tz(i,a));let u={flush:tm.Z_SYNC_FLUSH,finishFlush:tm.Z_SYNC_FLUSH};if("gzip"==c||"x-gzip"==c)return void r(h=new tz(i=i.pipe(tm.createGunzip(u)),a));if("deflate"==c||"x-deflate"==c){let t=e.pipe(new t0);t.once("data",function(e){r(h=new tz(i=(15&e[0])==8?i.pipe(tm.createInflate()):i.pipe(tm.createInflateRaw()),a))}),t.on("end",function(){h||r(h=new tz(i,a))});return}if("br"==c&&"function"==typeof tm.createBrotliDecompress)return void r(h=new tz(i=i.pipe(tm.createBrotliDecompress()),a));r(h=new tz(i,a))});let y=l.body;null===y?m.end():tj(y)?y.stream().pipe(m):Buffer.isBuffer(y)?(m.write(y),m.end()):y.pipe(m)})}function t3(e,t){e.destroy?e.destroy(t):(e.emit("error",t),e.end())}t2.isRedirect=function(e){return 301===e||302===e||303===e||307===e||308===e},t2.Promise=global.Promise;var t4=r(8354),t6=r(5191),t8=r(8355),t5=r(8767);let t9=e=>e instanceof t5.Y;var t7=r(3245);let re=(0,t4.deprecate)(()=>{},'Constructor "entries" argument is not spec-compliant and will be removed in next major release.');var rt=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class rr{constructor(e){p.add(this),m.set(this,new Map),e&&(re(),e.forEach(({name:e,value:t,fileName:r})=>this.append(e,t,r)))}static[(m=new WeakMap,p=new WeakSet,Symbol.hasInstance)](e){return!!(e&&(0,t7.T)(e.constructor)&&"FormData"===e[Symbol.toStringTag]&&(0,t7.T)(e.append)&&(0,t7.T)(e.set)&&(0,t7.T)(e.get)&&(0,t7.T)(e.getAll)&&(0,t7.T)(e.has)&&(0,t7.T)(e.delete)&&(0,t7.T)(e.entries)&&(0,t7.T)(e.values)&&(0,t7.T)(e.keys)&&(0,t7.T)(e[Symbol.iterator])&&(0,t7.T)(e.forEach))}append(e,t,r){rt(this,p,"m",g).call(this,{name:e,fileName:r,append:!0,rawValue:t,argsLength:arguments.length})}set(e,t,r){rt(this,p,"m",g).call(this,{name:e,fileName:r,append:!1,rawValue:t,argsLength:arguments.length})}get(e){let t=rt(this,m,"f").get(String(e));return t?t[0]:null}getAll(e){let t=rt(this,m,"f").get(String(e));return t?t.slice():[]}has(e){return rt(this,m,"f").has(String(e))}delete(e){rt(this,m,"f").delete(String(e))}*keys(){for(let e of rt(this,m,"f").keys())yield e}*entries(){for(let e of this.keys())for(let t of this.getAll(e))yield[e,t]}*values(){for(let[,e]of this)yield e}[(g=function({name:e,rawValue:t,append:r,fileName:n,argsLength:s}){let i,o=r?"append":"set";if(s<2)throw TypeError(`Failed to execute '${o}' on 'FormData': 2 arguments required, but only ${s} present.`);if(e=String(e),(0,t8.f)(t))i=void 0===n?t:new t6.Z([t],n,{type:t.type,lastModified:t.lastModified});else if(t9(t))i=new t6.Z([t],void 0===n?"blob":n,{type:t.type});else if(n)throw TypeError(`Failed to execute '${o}' on 'FormData': parameter 2 is not of type 'Blob'.`);else i=String(t);let a=rt(this,m,"f").get(e);if(!a||!r)return void rt(this,m,"f").set(e,[i]);a.push(i)},Symbol.iterator)](){return this.entries()}forEach(e,t){for(let[r,n]of this)e.call(t,n,r,this)}get[Symbol.toStringTag](){return"FormData"}[t4.inspect.custom](){return this[Symbol.toStringTag]}}var rn=r(4759),rs=r(9983),ri=r(3024);let ro="abcdefghijklmnopqrstuvwxyz0123456789",ra=function(){let e=16,t="";for(;e--;)t+=ro[Math.random()*ro.length|0];return t},rl=e=>Object.prototype.toString.call(e).slice(8,-1).toLowerCase(),rc=function(e){if("object"!==rl(e))return!1;let t=Object.getPrototypeOf(e);return null==t||(t.constructor&&t.constructor.toString())===Object.toString()},ru=e=>String(e).replace(/\r|\n/g,(e,t,r)=>"\r"===e&&"\n"!==r[t+1]||"\n"===e&&"\r"!==r[t-1]?"\r\n":e),rd=e=>String(e).replace(/\r/g,"%0D").replace(/\n/g,"%0A").replace(/"/g,"%22"),rh=e=>"function"==typeof e,rf=e=>!!(e&&"object"==typeof e&&rh(e.constructor)&&"File"===e[Symbol.toStringTag]&&rh(e.stream)&&null!=e.name&&null!=e.size&&null!=e.lastModified),rp=e=>!!(e&&rh(e.constructor)&&"FormData"===e[Symbol.toStringTag]&&rh(e.append)&&rh(e.getAll)&&rh(e.entries)&&rh(e[Symbol.iterator]));var rm=function(e,t,r,n,s){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},rg=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};let ry={enableAdditionalHeaders:!1};class rb{constructor(e,t,r){let n;if(y.add(this),b.set(this,"\r\n"),_.set(this,void 0),v.set(this,void 0),w.set(this,"-".repeat(2)),x.set(this,new TextEncoder),S.set(this,void 0),k.set(this,void 0),T.set(this,void 0),!rp(e))throw TypeError("Expected first argument to be a FormData instance.");if(rc(t)?r=t:n=t,n||(n=ra()),"string"!=typeof n)throw TypeError("Expected boundary argument to be a string.");if(r&&!rc(r))throw TypeError("Expected options argument to be an object.");rm(this,k,e,"f"),rm(this,T,{...ry,...r},"f"),rm(this,_,rg(this,x,"f").encode(rg(this,b,"f")),"f"),rm(this,v,rg(this,_,"f").byteLength,"f"),this.boundary=`form-data-boundary-${n}`,this.contentType=`multipart/form-data; boundary=${this.boundary}`,rm(this,S,rg(this,x,"f").encode(`${rg(this,w,"f")}${this.boundary}${rg(this,w,"f")}${rg(this,b,"f").repeat(2)}`),"f"),this.contentLength=String(this.getContentLength()),this.headers=Object.freeze({"Content-Type":this.contentType,"Content-Length":this.contentLength}),Object.defineProperties(this,{boundary:{writable:!1,configurable:!1},contentType:{writable:!1,configurable:!1},contentLength:{writable:!1,configurable:!1},headers:{writable:!1,configurable:!1}})}getContentLength(){let e=0;for(let[t,r]of rg(this,k,"f")){let n=rf(r)?r:rg(this,x,"f").encode(ru(r));e+=rg(this,y,"m",j).call(this,t,n).byteLength,e+=rf(n)?n.size:n.byteLength,e+=rg(this,v,"f")}return e+rg(this,S,"f").byteLength}*values(){for(let[e,t]of rg(this,k,"f").entries()){let r=rf(t)?t:rg(this,x,"f").encode(ru(t));yield rg(this,y,"m",j).call(this,e,r),yield r,yield rg(this,_,"f")}yield rg(this,S,"f")}async *encode(){for(let e of this.values())rf(e)?yield*e.stream():yield e}[(b=new WeakMap,_=new WeakMap,v=new WeakMap,w=new WeakMap,x=new WeakMap,S=new WeakMap,k=new WeakMap,T=new WeakMap,y=new WeakSet,j=function(e,t){let r="";return r+=`${rg(this,w,"f")}${this.boundary}${rg(this,b,"f")}Content-Disposition: form-data; name="${rd(e)}"`,rf(t)&&(r+=`; filename="${rd(t.name)}"${rg(this,b,"f")}Content-Type: ${t.type||"application/octet-stream"}`),!0===rg(this,T,"f").enableAdditionalHeaders&&(r+=`${rg(this,b,"f")}Content-Length: ${rf(t)?t.size:t.byteLength}`),rg(this,x,"f").encode(`${r}${rg(this,b,"f").repeat(2)}`)},Symbol.iterator)](){return this.values()}[Symbol.asyncIterator](){return this.encode()}}var r_=r(7075);class rv{constructor(e){this.body=e}get[Symbol.toStringTag](){return"MultipartBody"}}var rw=r(7830);let rx=!1;async function rS(e,...t){let{fileFromPath:n}=await r.e(915).then(r.bind(r,4915));return rx||(console.warn(`fileFromPath is deprecated; use fs.createReadStream(${JSON.stringify(e)}) instead`),rx=!0),await n(e,...t)}let rk=new rn({keepAlive:!0,timeout:3e5}),rT=new rn.HttpsAgent({keepAlive:!0,timeout:3e5});async function rj(e,t){let r=new rb(e),n=new rv(r_.Readable.from(r)),s={...t.headers,...r.headers,"Content-Length":r.contentLength};return{...t,body:n,headers:s}}let rE=()=>{i||function(e,t={auto:!1}){if(tc)throw Error(`you must \`import 'openai/shims/${e.kind}'\` before importing anything else from openai`);if(i)throw Error(`can't \`import 'openai/shims/${e.kind}'\` after \`import 'openai/shims/${i}'\``);tc=t.auto,i=e.kind,o=e.fetch,e.Request,e.Response,e.Headers,a=e.FormData,e.Blob,l=e.File,c=e.ReadableStream,u=e.getMultipartRequestOptions,d=e.getDefaultAgent,h=e.fileFromPath,f=e.isFsReadStream}(("undefined"==typeof AbortController&&(globalThis.AbortController=rs.AbortController),{kind:"node",fetch:t2,Request:tY,Response:tz,Headers:tq,FormData:rr,Blob:t5.Y,File:t6.Z,ReadableStream:rw.ReadableStream,getMultipartRequestOptions:rj,getDefaultAgent:e=>e.startsWith("https")?rT:rk,fileFromPath:rS,isFsReadStream:e=>e instanceof ri.ReadStream}),{auto:!0})};rE();class rC extends Error{}class rP extends rC{constructor(e,t,r,n){super(`${rP.makeMessage(e,t,r)}`),this.status=e,this.headers=n,this.request_id=n?.["x-request-id"],this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,r){let n=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):r;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,r,n){if(!e||!n)return new rA({message:r,cause:nv(t)});let s=t?.error;return 400===e?new rN(e,s,r,n):401===e?new rI(e,s,r,n):403===e?new r$(e,s,r,n):404===e?new rq(e,s,r,n):409===e?new rD(e,s,r,n):422===e?new rL(e,s,r,n):429===e?new rB(e,s,r,n):e>=500?new rM(e,s,r,n):new rP(e,s,r,n)}}class rR extends rP{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class rA extends rP{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class rO extends rA{constructor({message:e}={}){super({message:e??"Request timed out."})}}class rN extends rP{}class rI extends rP{}class r$ extends rP{}class rq extends rP{}class rD extends rP{}class rL extends rP{}class rB extends rP{}class rM extends rP{}class rF extends rC{constructor(){super("Could not parse response content as the length limit was reached")}}class rW extends rC{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}var rz=function(e,t,r,n,s){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},rU=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class rH{constructor(){E.set(this,void 0),this.buffer=new Uint8Array,rz(this,E,null,"f")}decode(e){let t;if(null==e)return[];let r=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?new TextEncoder().encode(e):e,n=new Uint8Array(this.buffer.length+r.length);n.set(this.buffer),n.set(r,this.buffer.length),this.buffer=n;let s=[];for(;null!=(t=function(e,t){for(let r=t??0;r<e.length;r++){if(10===e[r])return{preceding:r,index:r+1,carriage:!1};if(13===e[r])return{preceding:r,index:r+1,carriage:!0}}return null}(this.buffer,rU(this,E,"f")));){if(t.carriage&&null==rU(this,E,"f")){rz(this,E,t.index,"f");continue}if(null!=rU(this,E,"f")&&(t.index!==rU(this,E,"f")+1||t.carriage)){s.push(this.decodeText(this.buffer.slice(0,rU(this,E,"f")-1))),this.buffer=this.buffer.slice(rU(this,E,"f")),rz(this,E,null,"f");continue}let e=null!==rU(this,E,"f")?t.preceding-1:t.preceding,r=this.decodeText(this.buffer.slice(0,e));s.push(r),this.buffer=this.buffer.slice(t.index),rz(this,E,null,"f")}return s}decodeText(e){if(null==e)return"";if("string"==typeof e)return e;if("undefined"!=typeof Buffer){if(e instanceof Buffer)return e.toString();if(e instanceof Uint8Array)return Buffer.from(e).toString();throw new rC(`Unexpected: received non-Uint8Array (${e.constructor.name}) stream chunk in an environment with a global "Buffer" defined, which this library assumes to be Node. Please report this error.`)}if("undefined"!=typeof TextDecoder){if(e instanceof Uint8Array||e instanceof ArrayBuffer)return this.textDecoder??(this.textDecoder=new TextDecoder("utf8")),this.textDecoder.decode(e);throw new rC(`Unexpected: received non-Uint8Array/ArrayBuffer (${e.constructor.name}) in a web platform. Please report this error.`)}throw new rC("Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.")}flush(){return this.buffer.length?this.decode("\n"):[]}}function rV(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}E=new WeakMap,rH.NEWLINE_CHARS=new Set(["\n","\r"]),rH.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class rJ{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let r=!1;async function*n(){if(r)throw Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let n=!1;try{for await(let r of rK(e,t))if(!n){if(r.data.startsWith("[DONE]")){n=!0;continue}if(null===r.event||r.event.startsWith("response.")||r.event.startsWith("transcript.")){let t;try{t=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if(t&&t.error)throw new rP(void 0,t.error,void 0,nl(e.headers));yield t}else{let e;try{e=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if("error"==r.event)throw new rP(void 0,e.error,e.message,void 0);yield{event:r.event,data:e}}}n=!0}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;throw e}finally{n||t.abort()}}return new rJ(n,t)}static fromReadableStream(e,t){let r=!1;async function*n(){let t=new rH;for await(let r of rV(e))for(let e of t.decode(r))yield e;for(let e of t.flush())yield e}return new rJ(async function*(){if(r)throw Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let e=!1;try{for await(let t of n())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],r=this.iterator(),n=n=>({next:()=>{if(0===n.length){let n=r.next();e.push(n),t.push(n)}return n.shift()}});return[new rJ(()=>n(e),this.controller),new rJ(()=>n(t),this.controller)]}toReadableStream(){let e,t=this,r=new TextEncoder;return new c({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:n,done:s}=await e.next();if(s)return t.close();let i=r.encode(JSON.stringify(n)+"\n");t.enqueue(i)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*rK(e,t){if(!e.body)throw t.abort(),new rC("Attempted to iterate over a response with no body");let r=new rG,n=new rH;for await(let t of rX(rV(e.body)))for(let e of n.decode(t)){let t=r.decode(e);t&&(yield t)}for(let e of n.flush()){let t=r.decode(e);t&&(yield t)}}async function*rX(e){let t=new Uint8Array;for await(let r of e){let e;if(null==r)continue;let n=r instanceof ArrayBuffer?new Uint8Array(r):"string"==typeof r?new TextEncoder().encode(r):r,s=new Uint8Array(t.length+n.length);for(s.set(t),s.set(n,t.length),t=s;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class rG{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,r,n]=function(e,t){let r=e.indexOf(":");return -1!==r?[e.substring(0,r),t,e.substring(r+t.length)]:[e,"",""]}(e,":");return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}let rY=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob,rQ=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&rZ(e),rZ=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,r0=e=>rQ(e)||rY(e)||f(e);async function r1(e,t,r){var n;if(rQ(e=await e))return e;if(rY(e)){let n=await e.blob();t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()??"unknown_file");let s=rZ(n)?[await n.arrayBuffer()]:[n];return new l(s,t,r)}let s=await r2(e);if(t||(t=(r3((n=e).name)||r3(n.filename)||r3(n.path)?.split(/[\\/]/).pop())??"unknown_file"),!r?.type){let e=s[0]?.type;"string"==typeof e&&(r={...r,type:e})}return new l(s,t,r)}async function r2(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(rZ(e))t.push(await e.arrayBuffer());else if(r4(e))for await(let r of e)t.push(r);else throw Error(`Unexpected data type: ${typeof e}; constructor: ${e?.constructor?.name}; props: ${function(e){let t=Object.getOwnPropertyNames(e);return`[${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`);return t}let r3=e=>"string"==typeof e?e:"undefined"!=typeof Buffer&&e instanceof Buffer?String(e):void 0,r4=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],r6=e=>e&&"object"==typeof e&&e.body&&"MultipartBody"===e[Symbol.toStringTag],r8=async e=>{let t=await r5(e.body);return u(t,e)},r5=async e=>{let t=new a;return await Promise.all(Object.entries(e||{}).map(([e,r])=>r7(t,e,r))),t},r9=e=>{if(r0(e))return!0;if(Array.isArray(e))return e.some(r9);if(e&&"object"==typeof e){for(let t in e)if(r9(e[t]))return!0}return!1},r7=async(e,t,r)=>{if(void 0!==r){if(null==r)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof r||"number"==typeof r||"boolean"==typeof r)e.append(t,String(r));else if(r0(r)){let n=await r1(r);e.append(t,n)}else if(Array.isArray(r))await Promise.all(r.map(r=>r7(e,t+"[]",r)));else if("object"==typeof r)await Promise.all(Object.entries(r).map(([r,n])=>r7(e,`${t}[${r}]`,n)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${r} instead`)}};var ne=function(e,t,r,n,s){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},nt=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};async function nr(e){let{response:t}=e;if(e.options.stream)return(nj("response",t.status,t.url,t.headers,t.body),e.options.__streamClass)?e.options.__streamClass.fromSSEResponse(t,e.controller):rJ.fromSSEResponse(t,e.controller);if(204===t.status)return null;if(e.options.__binaryResponse)return t;let r=t.headers.get("content-type"),n=r?.split(";")[0]?.trim();if(n?.includes("application/json")||n?.endsWith("+json")){let e=await t.json();return nj("response",t.status,t.url,t.headers,e),nn(e,t)}let s=await t.text();return nj("response",t.status,t.url,t.headers,s),s}function nn(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}rE();class ns extends Promise{constructor(e,t=nr){super(e=>{e(null)}),this.responsePromise=e,this.parseResponse=t}_thenUnwrap(e){return new ns(this.responsePromise,async t=>nn(e(await this.parseResponse(t),t),t.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(this.parseResponse)),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}class ni{constructor({baseURL:e,maxRetries:t=2,timeout:r=6e5,httpAgent:n,fetch:s}){this.baseURL=e,this.maxRetries=n_("maxRetries",t),this.timeout=n_("timeout",r),this.httpAgent=n,this.fetch=s??o}authHeaders(e){return{}}defaultHeaders(e){return{Accept:"application/json","Content-Type":"application/json","User-Agent":this.getUserAgent(),...np(),...this.authHeaders(e)}}validateHeaders(e,t){}defaultIdempotencyKey(){return`stainless-node-retry-${nE()}`}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,r){return this.request(Promise.resolve(r).then(async r=>{let n=r&&rZ(r?.body)?new DataView(await r.body.arrayBuffer()):r?.body instanceof DataView?r.body:r?.body instanceof ArrayBuffer?new DataView(r.body):r&&ArrayBuffer.isView(r?.body)?new DataView(r.body.buffer):r?.body;return{method:e,path:t,...r,body:n}}))}getAPIList(e,t,r){return this.requestAPIList(t,{method:"get",path:e,...r})}calculateContentLength(e){if("string"==typeof e){if("undefined"!=typeof Buffer)return Buffer.byteLength(e,"utf8").toString();if("undefined"!=typeof TextEncoder)return new TextEncoder().encode(e).length.toString()}else if(ArrayBuffer.isView(e))return e.byteLength.toString();return null}buildRequest(e,{retryCount:t=0}={}){let r={...e},{method:n,path:s,query:i,headers:o={}}=r,a=ArrayBuffer.isView(r.body)||r.__binaryRequest&&"string"==typeof r.body?r.body:r6(r.body)?r.body.body:r.body?JSON.stringify(r.body,null,2):null,l=this.calculateContentLength(a),c=this.buildURL(s,i);"timeout"in r&&n_("timeout",r.timeout),r.timeout=r.timeout??this.timeout;let u=r.httpAgent??this.httpAgent??d(c),h=r.timeout+1e3;"number"==typeof u?.options?.timeout&&h>(u.options.timeout??0)&&(u.options.timeout=h),this.idempotencyHeader&&"get"!==n&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),o[this.idempotencyHeader]=e.idempotencyKey);let f=this.buildHeaders({options:r,headers:o,contentLength:l,retryCount:t});return{req:{method:n,...a&&{body:a},headers:f,...u&&{agent:u},signal:r.signal??null},url:c,timeout:r.timeout}}buildHeaders({options:e,headers:t,contentLength:r,retryCount:n}){let s={};r&&(s["content-length"]=r);let o=this.defaultHeaders(e);return nk(s,o),nk(s,t),r6(e.body)&&"node"!==i&&delete s["content-type"],void 0===nR(o,"x-stainless-retry-count")&&void 0===nR(t,"x-stainless-retry-count")&&(s["x-stainless-retry-count"]=String(n)),void 0===nR(o,"x-stainless-timeout")&&void 0===nR(t,"x-stainless-timeout")&&e.timeout&&(s["x-stainless-timeout"]=String(Math.trunc(e.timeout/1e3))),this.validateHeaders(s,t),s}async prepareOptions(e){}async prepareRequest(e,{url:t,options:r}){}parseHeaders(e){return e?Symbol.iterator in e?Object.fromEntries(Array.from(e).map(e=>[...e])):{...e}:{}}makeStatusError(e,t,r,n){return rP.generate(e,t,r,n)}request(e,t=null){return new ns(this.makeRequest(e,t))}async makeRequest(e,t){let r=await e,n=r.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(r);let{req:s,url:i,timeout:o}=this.buildRequest(r,{retryCount:n-t});if(await this.prepareRequest(s,{url:i,options:r}),nj("request",i,r,s.headers),r.signal?.aborted)throw new rR;let a=new AbortController,l=await this.fetchWithTimeout(i,s,o,a).catch(nv);if(l instanceof Error){if(r.signal?.aborted)throw new rR;if(t)return this.retryRequest(r,t);if("AbortError"===l.name)throw new rO;throw new rA({cause:l})}let c=nl(l.headers);if(!l.ok){if(t&&this.shouldRetry(l)){let e=`retrying, ${t} attempts remaining`;return nj(`response (error; ${e})`,l.status,i,c),this.retryRequest(r,t,c)}let e=await l.text().catch(e=>nv(e).message),n=nm(e),s=n?void 0:e,o=t?"(error; no more retries left)":"(error; not retryable)";throw nj(`response (error; ${o})`,l.status,i,c,s),this.makeStatusError(l.status,n,s,c)}return{response:l,options:r,controller:a}}requestAPIList(e,t){return new na(this,this.makeRequest(t,null),e)}buildURL(e,t){let r=new URL(ny(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),n=this.defaultQuery();return nx(n)||(t={...n,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}stringifyQuery(e){return Object.entries(e).filter(([e,t])=>void 0!==t).map(([e,t])=>{if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return`${encodeURIComponent(e)}=${encodeURIComponent(t)}`;if(null===t)return`${encodeURIComponent(e)}=`;throw new rC(`Cannot stringify type ${typeof t}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}async fetchWithTimeout(e,t,r,n){let{signal:s,...i}=t||{};s&&s.addEventListener("abort",()=>n.abort());let o=setTimeout(()=>n.abort(),r),a={signal:n.signal,...i};return a.method&&(a.method=a.method.toUpperCase()),this.fetch.call(void 0,e,a).finally(()=>{clearTimeout(o)})}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,r){let n,s=r?.["retry-after-ms"];if(s){let e=parseFloat(s);Number.isNaN(e)||(n=e)}let i=r?.["retry-after"];if(i&&!n){let e=parseFloat(i);n=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let r=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,r)}return await nb(n),this.makeRequest(e,t-1)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}getUserAgent(){return`${this.constructor.name}/JS ${tl}`}}class no{constructor(e,t,r,n){C.set(this,void 0),ne(this,C,e,"f"),this.options=n,this.response=t,this.body=r}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageInfo()}async getNextPage(){let e=this.nextPageInfo();if(!e)throw new rC("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");let t={...this.options};if("params"in e&&"object"==typeof t.query)t.query={...t.query,...e.params};else if("url"in e){for(let[r,n]of[...Object.entries(t.query||{}),...e.url.searchParams.entries()])e.url.searchParams.set(r,n);t.query=void 0,t.path=e.url.toString()}return await nt(this,C,"f").requestAPIList(this.constructor,t)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(C=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class na extends ns{constructor(e,t,r){super(t,async t=>new r(e,t.response,await nr(t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}let nl=e=>new Proxy(Object.fromEntries(e.entries()),{get(e,t){let r=t.toString();return e[r.toLowerCase()]||e[r]}}),nc={method:!0,path:!0,query:!0,body:!0,headers:!0,maxRetries:!0,stream:!0,timeout:!0,httpAgent:!0,signal:!0,idempotencyKey:!0,__metadata:!0,__binaryRequest:!0,__binaryResponse:!0,__streamClass:!0},nu=e=>"object"==typeof e&&null!==e&&!nx(e)&&Object.keys(e).every(e=>nS(nc,e)),nd=()=>{if("undefined"!=typeof Deno&&null!=Deno.build)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tl,"X-Stainless-OS":nf(Deno.build.os),"X-Stainless-Arch":nh(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tl,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":process.version};if("[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0))return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tl,"X-Stainless-OS":nf(process.platform),"X-Stainless-Arch":nh(process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":process.version};let e=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let r=t.exec(navigator.userAgent);if(r){let t=r[1]||0,n=r[2]||0,s=r[3]||0;return{browser:e,version:`${t}.${n}.${s}`}}}return null}();return e?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tl,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${e.browser}`,"X-Stainless-Runtime-Version":e.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":tl,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},nh=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",nf=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",np=()=>s??(s=nd()),nm=e=>{try{return JSON.parse(e)}catch(e){return}},ng=/^[a-z][a-z0-9+.-]*:/i,ny=e=>ng.test(e),nb=e=>new Promise(t=>setTimeout(t,e)),n_=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new rC(`${e} must be an integer`);if(t<0)throw new rC(`${e} must be a positive integer`);return t},nv=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e)try{return Error(JSON.stringify(e))}catch{}return Error(e)},nw=e=>"undefined"!=typeof process?process.env?.[e]?.trim()??void 0:"undefined"!=typeof Deno?Deno.env?.get?.(e)?.trim():void 0;function nx(e){if(!e)return!0;for(let t in e)return!1;return!0}function nS(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function nk(e,t){for(let r in t){if(!nS(t,r))continue;let n=r.toLowerCase();if(!n)continue;let s=t[r];null===s?delete e[n]:void 0!==s&&(e[n]=s)}}let nT=new Set(["authorization","api-key"]);function nj(e,...t){"undefined"!=typeof process&&process?.env?.DEBUG==="true"&&console.log(`OpenAI:DEBUG:${e}`,...t.map(e=>{if(!e)return e;if(e.headers){let t={...e,headers:{...e.headers}};for(let r in e.headers)nT.has(r.toLowerCase())&&(t.headers[r]="REDACTED");return t}let t=null;for(let r in e)nT.has(r.toLowerCase())&&(t??(t={...e}),t[r]="REDACTED");return t??e}))}let nE=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}),nC=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,nP=e=>"function"==typeof e?.get,nR=(e,t)=>{let r=t.toLowerCase();if(nP(e)){let n=t[0]?.toUpperCase()+t.substring(1).replace(/([^\w])(\w)/g,(e,t,r)=>t+r.toUpperCase());for(let s of[t,r,t.toUpperCase(),n]){let t=e.get(s);if(t)return t}}for(let[n,s]of Object.entries(e))if(n.toLowerCase()===r){if(Array.isArray(s)){if(s.length<=1)return s[0];return console.warn(`Received ${s.length} entries for the ${t} header, using the first entry.`),s[0]}return s}},nA=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),r=t.length,n=new Uint8Array(r);for(let e=0;e<r;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}};function nO(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}class nN{constructor(e){this._client=e}}class nI extends nN{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class n$ extends nN{list(e,t={},r){return nu(t)?this.list(e,{},t):this._client.getAPIList(`/chat/completions/${e}/messages`,nM,{query:t,...r})}}class nq extends no{constructor(e,t,r,n){super(e,t,r,n),this.data=r.data||[],this.object=r.object}getPaginatedItems(){return this.data??[]}nextPageParams(){return null}nextPageInfo(){return null}}class nD extends no{constructor(e,t,r,n){super(e,t,r,n),this.data=r.data||[],this.has_more=r.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageParams(){let e=this.nextPageInfo();if(!e)return null;if("params"in e)return e.params;let t=Object.fromEntries(e.url.searchParams);return Object.keys(t).length?t:null}nextPageInfo(){let e=this.getPaginatedItems();if(!e.length)return null;let t=e[e.length-1]?.id;return t?{params:{after:t}}:null}}class nL extends nN{constructor(){super(...arguments),this.messages=new n$(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(`/chat/completions/${e}`,t)}update(e,t,r){return this._client.post(`/chat/completions/${e}`,{body:t,...r})}list(e={},t){return nu(e)?this.list({},e):this._client.getAPIList("/chat/completions",nB,{query:e,...t})}del(e,t){return this._client.delete(`/chat/completions/${e}`,t)}}class nB extends nD{}class nM extends nD{}nL.ChatCompletionsPage=nB,nL.Messages=n$;class nF extends nN{constructor(){super(...arguments),this.completions=new nL(this._client)}}nF.Completions=nL,nF.ChatCompletionsPage=nB;class nW extends nN{create(e,t){let r=!!e.encoding_format,n=r?e.encoding_format:"base64";r&&nj("Request","User defined encoding_format:",e.encoding_format);let s=this._client.post("/embeddings",{body:{...e,encoding_format:n},...t});return r?s:(nj("response","Decoding base64 embeddings to float32 array"),s._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=nA(t)}),e)))}}class nz extends nN{create(e,t){return this._client.post("/files",r8({body:e,...t}))}retrieve(e,t){return this._client.get(`/files/${e}`,t)}list(e={},t){return nu(e)?this.list({},e):this._client.getAPIList("/files",nU,{query:e,...t})}del(e,t){return this._client.delete(`/files/${e}`,t)}content(e,t){return this._client.get(`/files/${e}/content`,{...t,headers:{Accept:"application/binary",...t?.headers},__binaryResponse:!0})}retrieveContent(e,t){return this._client.get(`/files/${e}/content`,t)}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:r=18e5}={}){let n=new Set(["processed","error","deleted"]),s=Date.now(),i=await this.retrieve(e);for(;!i.status||!n.has(i.status);)if(await nb(t),i=await this.retrieve(e),Date.now()-s>r)throw new rO({message:`Giving up on waiting for file ${e} to finish processing after ${r} milliseconds.`});return i}}class nU extends nD{}nz.FileObjectsPage=nU;class nH extends nN{createVariation(e,t){return this._client.post("/images/variations",r8({body:e,...t}))}edit(e,t){return this._client.post("/images/edits",r8({body:e,...t}))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class nV extends nN{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:{Accept:"application/octet-stream",...t?.headers},__binaryResponse:!0})}}class nJ extends nN{create(e,t){return this._client.post("/audio/transcriptions",r8({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}}))}}class nK extends nN{create(e,t){return this._client.post("/audio/translations",r8({body:e,...t,__metadata:{model:e.model}}))}}class nX extends nN{constructor(){super(...arguments),this.transcriptions=new nJ(this._client),this.translations=new nK(this._client),this.speech=new nV(this._client)}}nX.Transcriptions=nJ,nX.Translations=nK,nX.Speech=nV;class nG extends nN{create(e,t){return this._client.post("/moderations",{body:e,...t})}}class nY extends nN{retrieve(e,t){return this._client.get(`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",nQ,e)}del(e,t){return this._client.delete(`/models/${e}`,t)}}class nQ extends nq{}nY.ModelsPage=nQ;class nZ extends nN{}class n0 extends nN{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class n1 extends nN{constructor(){super(...arguments),this.graders=new n0(this._client)}}n1.Graders=n0;class n2 extends nN{create(e,t,r){return this._client.getAPIList(`/fine_tuning/checkpoints/${e}/permissions`,n3,{body:t,method:"post",...r})}retrieve(e,t={},r){return nu(t)?this.retrieve(e,{},t):this._client.get(`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...r})}del(e,t,r){return this._client.delete(`/fine_tuning/checkpoints/${e}/permissions/${t}`,r)}}class n3 extends nq{}n2.PermissionCreateResponsesPage=n3;class n4 extends nN{constructor(){super(...arguments),this.permissions=new n2(this._client)}}n4.Permissions=n2,n4.PermissionCreateResponsesPage=n3;class n6 extends nN{list(e,t={},r){return nu(t)?this.list(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/checkpoints`,n8,{query:t,...r})}}class n8 extends nD{}n6.FineTuningJobCheckpointsPage=n8;class n5 extends nN{constructor(){super(...arguments),this.checkpoints=new n6(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(`/fine_tuning/jobs/${e}`,t)}list(e={},t){return nu(e)?this.list({},e):this._client.getAPIList("/fine_tuning/jobs",n9,{query:e,...t})}cancel(e,t){return this._client.post(`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},r){return nu(t)?this.listEvents(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/events`,n7,{query:t,...r})}pause(e,t){return this._client.post(`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(`/fine_tuning/jobs/${e}/resume`,t)}}class n9 extends nD{}class n7 extends nD{}n5.FineTuningJobsPage=n9,n5.FineTuningJobEventsPage=n7,n5.Checkpoints=n6,n5.FineTuningJobCheckpointsPage=n8;class se extends nN{constructor(){super(...arguments),this.methods=new nZ(this._client),this.jobs=new n5(this._client),this.checkpoints=new n4(this._client),this.alpha=new n1(this._client)}}se.Methods=nZ,se.Jobs=n5,se.FineTuningJobsPage=n9,se.FineTuningJobEventsPage=n7,se.Checkpoints=n4,se.Alpha=n1;class st extends nN{}class sr extends nN{constructor(){super(...arguments),this.graderModels=new st(this._client)}}sr.GraderModels=st;let sn=async e=>{let t=await Promise.allSettled(e),r=t.filter(e=>"rejected"===e.status);if(r.length){for(let e of r)console.error(e.reason);throw Error(`${r.length} promise(s) failed - see the above errors`)}let n=[];for(let e of t)"fulfilled"===e.status&&n.push(e.value);return n};class ss extends nN{create(e,t,r){return this._client.post(`/vector_stores/${e}/files`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}retrieve(e,t,r){return this._client.get(`/vector_stores/${e}/files/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}update(e,t,r,n){return this._client.post(`/vector_stores/${e}/files/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}list(e,t={},r){return nu(t)?this.list(e,{},t):this._client.getAPIList(`/vector_stores/${e}/files`,si,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}del(e,t,r){return this._client.delete(`/vector_stores/${e}/files/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}async createAndPoll(e,t,r){let n=await this.create(e,t,r);return await this.poll(e,n.id,r)}async poll(e,t,r){let n={...r?.headers,"X-Stainless-Poll-Helper":"true"};for(r?.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){let s=await this.retrieve(e,t,{...r,headers:n}).withResponse(),i=s.data;switch(i.status){case"in_progress":let o=5e3;if(r?.pollIntervalMs)o=r.pollIntervalMs;else{let e=s.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(o=t)}}await nb(o);break;case"failed":case"completed":return i}}}async upload(e,t,r){let n=await this._client.files.create({file:t,purpose:"assistants"},r);return this.create(e,{file_id:n.id},r)}async uploadAndPoll(e,t,r){let n=await this.upload(e,t,r);return await this.poll(e,n.id,r)}content(e,t,r){return this._client.getAPIList(`/vector_stores/${e}/files/${t}/content`,so,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}}class si extends nD{}class so extends nq{}ss.VectorStoreFilesPage=si,ss.FileContentResponsesPage=so;class sa extends nN{create(e,t,r){return this._client.post(`/vector_stores/${e}/file_batches`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}retrieve(e,t,r){return this._client.get(`/vector_stores/${e}/file_batches/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}cancel(e,t,r){return this._client.post(`/vector_stores/${e}/file_batches/${t}/cancel`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}async createAndPoll(e,t,r){let n=await this.create(e,t);return await this.poll(e,n.id,r)}listFiles(e,t,r={},n){return nu(r)?this.listFiles(e,t,{},r):this._client.getAPIList(`/vector_stores/${e}/file_batches/${t}/files`,si,{query:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}async poll(e,t,r){let n={...r?.headers,"X-Stainless-Poll-Helper":"true"};for(r?.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){let{data:s,response:i}=await this.retrieve(e,t,{...r,headers:n}).withResponse();switch(s.status){case"in_progress":let o=5e3;if(r?.pollIntervalMs)o=r.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(o=t)}}await nb(o);break;case"failed":case"cancelled":case"completed":return s}}}async uploadAndPoll(e,{files:t,fileIds:r=[]},n){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let s=Math.min(n?.maxConcurrency??5,t.length),i=this._client,o=t.values(),a=[...r];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},n);a.push(e.id)}}let c=Array(s).fill(o).map(l);return await sn(c),await this.createAndPoll(e,{file_ids:a})}}class sl extends nN{constructor(){super(...arguments),this.files=new ss(this._client),this.fileBatches=new sa(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}retrieve(e,t){return this._client.get(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}update(e,t,r){return this._client.post(`/vector_stores/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}list(e={},t){return nu(e)?this.list({},e):this._client.getAPIList("/vector_stores",sc,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}del(e,t){return this._client.delete(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}search(e,t,r){return this._client.getAPIList(`/vector_stores/${e}/search`,su,{body:t,method:"post",...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}}class sc extends nD{}class su extends nq{}sl.VectorStoresPage=sc,sl.VectorStoreSearchResponsesPage=su,sl.Files=ss,sl.VectorStoreFilesPage=si,sl.FileContentResponsesPage=so,sl.FileBatches=sa;class sd extends nN{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}retrieve(e,t){return this._client.get(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}update(e,t,r){return this._client.post(`/assistants/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}list(e={},t){return nu(e)?this.list({},e):this._client.getAPIList("/assistants",sh,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}del(e,t){return this._client.delete(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}}class sh extends nD{}function sf(e){return"function"==typeof e.parse}sd.AssistantsPage=sh;let sp=e=>e?.role==="assistant",sm=e=>e?.role==="function",sg=e=>e?.role==="tool";var sy=function(e,t,r,n,s){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},sb=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class s_{constructor(){P.add(this),this.controller=new AbortController,R.set(this,void 0),A.set(this,()=>{}),O.set(this,()=>{}),N.set(this,void 0),I.set(this,()=>{}),$.set(this,()=>{}),q.set(this,{}),D.set(this,!1),L.set(this,!1),B.set(this,!1),M.set(this,!1),sy(this,R,new Promise((e,t)=>{sy(this,A,e,"f"),sy(this,O,t,"f")}),"f"),sy(this,N,new Promise((e,t)=>{sy(this,I,e,"f"),sy(this,$,t,"f")}),"f"),sb(this,R,"f").catch(()=>{}),sb(this,N,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},sb(this,P,"m",F).bind(this))},0)}_connected(){this.ended||(sb(this,A,"f").call(this),this._emit("connect"))}get ended(){return sb(this,D,"f")}get errored(){return sb(this,L,"f")}get aborted(){return sb(this,B,"f")}abort(){this.controller.abort()}on(e,t){return(sb(this,q,"f")[e]||(sb(this,q,"f")[e]=[])).push({listener:t}),this}off(e,t){let r=sb(this,q,"f")[e];if(!r)return this;let n=r.findIndex(e=>e.listener===t);return n>=0&&r.splice(n,1),this}once(e,t){return(sb(this,q,"f")[e]||(sb(this,q,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,r)=>{sy(this,M,!0,"f"),"error"!==e&&this.once("error",r),this.once(e,t)})}async done(){sy(this,M,!0,"f"),await sb(this,N,"f")}_emit(e,...t){if(sb(this,D,"f"))return;"end"===e&&(sy(this,D,!0,"f"),sb(this,I,"f").call(this));let r=sb(this,q,"f")[e];if(r&&(sb(this,q,"f")[e]=r.filter(e=>!e.once),r.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];sb(this,M,"f")||r?.length||Promise.reject(e),sb(this,O,"f").call(this,e),sb(this,$,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];sb(this,M,"f")||r?.length||Promise.reject(e),sb(this,O,"f").call(this,e),sb(this,$,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function sv(e){return e?.$brand==="auto-parseable-response-format"}function sw(e){return e?.$brand==="auto-parseable-tool"}function sx(e,t){let r=e.choices.map(e=>{var r,n;if("length"===e.finish_reason)throw new rF;if("content_filter"===e.finish_reason)throw new rW;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let r=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:sw(r)?r.$parseRaw(t.function.arguments):r?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(r=t,n=e.message.content,r.response_format?.type!=="json_schema"?null:r.response_format?.type==="json_schema"?"$parseRaw"in r.response_format?r.response_format.$parseRaw(n):JSON.parse(n):null):null}}});return{...e,choices:r}}function sS(e){return!!sv(e.response_format)||(e.tools?.some(e=>sw(e)||"function"===e.type&&!0===e.function.strict)??!1)}R=new WeakMap,A=new WeakMap,O=new WeakMap,N=new WeakMap,I=new WeakMap,$=new WeakMap,q=new WeakMap,D=new WeakMap,L=new WeakMap,B=new WeakMap,M=new WeakMap,P=new WeakSet,F=function(e){if(sy(this,L,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new rR),e instanceof rR)return sy(this,B,!0,"f"),this._emit("abort",e);if(e instanceof rC)return this._emit("error",e);if(e instanceof Error){let t=new rC(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new rC(String(e)))};var sk=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class sT extends s_{constructor(){super(...arguments),W.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),(sm(e)||sg(e))&&e.content)this._emit("functionCallResult",e.content);else if(sp(e)&&e.function_call)this._emit("functionCall",e.function_call);else if(sp(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new rC("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),sk(this,W,"m",z).call(this)}async finalMessage(){return await this.done(),sk(this,W,"m",U).call(this)}async finalFunctionCall(){return await this.done(),sk(this,W,"m",H).call(this)}async finalFunctionCallResult(){return await this.done(),sk(this,W,"m",V).call(this)}async totalUsage(){return await this.done(),sk(this,W,"m",J).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=sk(this,W,"m",U).call(this);t&&this._emit("finalMessage",t);let r=sk(this,W,"m",z).call(this);r&&this._emit("finalContent",r);let n=sk(this,W,"m",H).call(this);n&&this._emit("finalFunctionCall",n);let s=sk(this,W,"m",V).call(this);null!=s&&this._emit("finalFunctionCallResult",s),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",sk(this,W,"m",J).call(this))}async _createChatCompletion(e,t,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),sk(this,W,"m",K).call(this,t);let s=await e.chat.completions.create({...t,stream:!1},{...r,signal:this.controller.signal});return this._connected(),this._addChatCompletion(sx(s,t))}async _runChatCompletion(e,t,r){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,r)}async _runFunctions(e,t,r){let n="function",{function_call:s="auto",stream:i,...o}=t,a="string"!=typeof s&&s?.name,{maxChatCompletions:l=10}=r||{},c={};for(let e of t.functions)c[e.name||e.function.name]=e;let u=t.functions.map(e=>({name:e.name||e.function.name,parameters:e.parameters,description:e.description}));for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t,i=await this._createChatCompletion(e,{...o,function_call:s,functions:u,messages:[...this.messages]},r),l=i.choices[0]?.message;if(!l)throw new rC("missing message in ChatCompletion response");if(!l.function_call)return;let{name:d,arguments:h}=l.function_call,f=c[d];if(f){if(a&&a!==d){let e=`Invalid function_call: ${JSON.stringify(d)}. ${JSON.stringify(a)} requested. Please try again`;this._addMessage({role:n,name:d,content:e});continue}}else{let e=`Invalid function_call: ${JSON.stringify(d)}. Available options are: ${u.map(e=>JSON.stringify(e.name)).join(", ")}. Please try again`;this._addMessage({role:n,name:d,content:e});continue}try{t=sf(f)?await f.parse(h):h}catch(e){this._addMessage({role:n,name:d,content:e instanceof Error?e.message:String(e)});continue}let p=await f.function(t,this),m=sk(this,W,"m",X).call(this,p);if(this._addMessage({role:n,name:d,content:m}),a)return}}async _runTools(e,t,r){let n="tool",{tool_choice:s="auto",stream:i,...o}=t,a="string"!=typeof s&&s?.function?.name,{maxChatCompletions:l=10}=r||{},c=t.tools.map(e=>{if(sw(e)){if(!e.$callback)throw new rC("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(let e of c)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);let d="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...o,tool_choice:s,tools:d,messages:[...this.messages]},r),i=t.choices[0]?.message;if(!i)throw new rC("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let r=e.id,{name:s,arguments:i}=e.function,o=u[s];if(o){if(a&&a!==s){let e=`Invalid tool_call: ${JSON.stringify(s)}. ${JSON.stringify(a)} requested. Please try again`;this._addMessage({role:n,tool_call_id:r,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(s)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:r,content:e});continue}try{t=sf(o)?await o.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:n,tool_call_id:r,content:e});continue}let l=await o.function(t,this),c=sk(this,W,"m",X).call(this,l);if(this._addMessage({role:n,tool_call_id:r,content:c}),a)return}}}}W=new WeakSet,z=function(){return sk(this,W,"m",U).call(this).content??null},U=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(sp(t)){let{function_call:e,...r}=t,n={...r,content:t.content??null,refusal:t.refusal??null};return e&&(n.function_call=e),n}}throw new rC("stream ended without producing a ChatCompletionMessage with role=assistant")},H=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(sp(t)&&t?.function_call)return t.function_call;if(sp(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},V=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(sm(t)&&null!=t.content||sg(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},J=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},K=function(e){if(null!=e.n&&e.n>1)throw new rC("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},X=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class sj extends sT{static runFunctions(e,t,r){let n=new sj,s={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runFunctions"}};return n._run(()=>n._runFunctions(e,t,s)),n}static runTools(e,t,r){let n=new sj,s={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,s)),n}_addMessage(e,t=!0){super._addMessage(e,t),sp(e)&&e.content&&this._emit("content",e.content)}}let sE={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class sC extends Error{}class sP extends Error{}let sR=(e,t)=>{let r=e.length,n=0,s=e=>{throw new sC(`${e} at position ${n}`)},i=e=>{throw new sP(`${e} at position ${n}`)},o=()=>(d(),n>=r&&s("Unexpected end of input"),'"'===e[n])?a():"{"===e[n]?l():"["===e[n]?c():"null"===e.substring(n,n+4)||sE.NULL&t&&r-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||sE.BOOL&t&&r-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||sE.BOOL&t&&r-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||sE.INFINITY&t&&r-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||sE.MINUS_INFINITY&t&&1<r-n&&r-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||sE.NAN&t&&r-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u(),a=()=>{let o=n,a=!1;for(n++;n<r&&('"'!==e[n]||a&&"\\"===e[n-1]);)a="\\"===e[n]&&!a,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(o,++n-Number(a)))}catch(e){i(String(e))}else if(sE.STR&t)try{return JSON.parse(e.substring(o,n-Number(a))+'"')}catch(t){return JSON.parse(e.substring(o,e.lastIndexOf("\\"))+'"')}s("Unterminated string literal")},l=()=>{n++,d();let i={};try{for(;"}"!==e[n];){if(d(),n>=r&&sE.OBJ&t)return i;let s=a();d(),n++;try{let e=o();Object.defineProperty(i,s,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(sE.OBJ&t)return i;throw e}d(),","===e[n]&&n++}}catch(e){if(sE.OBJ&t)return i;s("Expected '}' at end of object")}return n++,i},c=()=>{n++;let r=[];try{for(;"]"!==e[n];)r.push(o()),d(),","===e[n]&&n++}catch(e){if(sE.ARR&t)return r;s("Expected ']' at end of array")}return n++,r},u=()=>{if(0===n){"-"===e&&sE.NUM&t&&s("Not sure what '-' is");try{return JSON.parse(e)}catch(r){if(sE.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(r))}}let o=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=r||sE.NUM&t||s("Unterminated number literal");try{return JSON.parse(e.substring(o,n))}catch(r){"-"===e.substring(o,n)&&sE.NUM&t&&s("Not sure what '-' is");try{return JSON.parse(e.substring(o,e.lastIndexOf("e")))}catch(e){i(String(e))}}},d=()=>{for(;n<r&&" \n\r	".includes(e[n]);)n++};return o()},sA=e=>(function(e,t=sE.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return sR(e.trim(),t)})(e,sE.ALL^sE.NUM);var sO=function(e,t,r,n,s){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},sN=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class sI extends sT{constructor(e){super(),G.add(this),Y.set(this,void 0),Q.set(this,void 0),Z.set(this,void 0),sO(this,Y,e,"f"),sO(this,Q,[],"f")}get currentChatCompletionSnapshot(){return sN(this,Z,"f")}static fromReadableStream(e){let t=new sI(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,r){let n=new sI(t);return n._run(()=>n._runChatCompletion(e,{...t,stream:!0},{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(e,t,r){super._createChatCompletion;let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),sN(this,G,"m",ee).call(this);let s=await e.chat.completions.create({...t,stream:!0},{...r,signal:this.controller.signal});for await(let e of(this._connected(),s))sN(this,G,"m",er).call(this,e);if(s.controller.signal?.aborted)throw new rR;return this._addChatCompletion(sN(this,G,"m",ei).call(this))}async _fromReadableStream(e,t){let r,n=t?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),sN(this,G,"m",ee).call(this),this._connected();let s=rJ.fromReadableStream(e,this.controller);for await(let e of s)r&&r!==e.id&&this._addChatCompletion(sN(this,G,"m",ei).call(this)),sN(this,G,"m",er).call(this,e),r=e.id;if(s.controller.signal?.aborted)throw new rR;return this._addChatCompletion(sN(this,G,"m",ei).call(this))}[(Y=new WeakMap,Q=new WeakMap,Z=new WeakMap,G=new WeakSet,ee=function(){this.ended||sO(this,Z,void 0,"f")},et=function(e){let t=sN(this,Q,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},sN(this,Q,"f")[e.index]=t),t},er=function(e){if(this.ended)return;let t=sN(this,G,"m",ea).call(this,e);for(let r of(this._emit("chunk",e,t),e.choices)){let e=t.choices[r.index];null!=r.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",r.delta.content,e.message.content),this._emit("content.delta",{delta:r.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=r.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:r.delta.refusal,snapshot:e.message.refusal}),r.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:r.logprobs?.content,snapshot:e.logprobs?.content??[]}),r.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:r.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let n=sN(this,G,"m",et).call(this,e);for(let t of(e.finish_reason&&(sN(this,G,"m",es).call(this,e),null!=n.current_tool_call_index&&sN(this,G,"m",en).call(this,e,n.current_tool_call_index)),r.delta.tool_calls??[]))n.current_tool_call_index!==t.index&&(sN(this,G,"m",es).call(this,e),null!=n.current_tool_call_index&&sN(this,G,"m",en).call(this,e,n.current_tool_call_index)),n.current_tool_call_index=t.index;for(let t of r.delta.tool_calls??[]){let r=e.message.tool_calls?.[t.index];r?.type&&(r?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:r.function?.name,index:t.index,arguments:r.function.arguments,parsed_arguments:r.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):r?.type)}}},en=function(e,t){if(sN(this,G,"m",et).call(this,e).done_tool_calls.has(t))return;let r=e.message.tool_calls?.[t];if(!r)throw Error("no tool call snapshot");if(!r.type)throw Error("tool call snapshot missing `type`");if("function"===r.type){let e=sN(this,Y,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===r.function.name);this._emit("tool_calls.function.arguments.done",{name:r.function.name,index:t,arguments:r.function.arguments,parsed_arguments:sw(e)?e.$parseRaw(r.function.arguments):e?.function.strict?JSON.parse(r.function.arguments):null})}else r.type},es=function(e){let t=sN(this,G,"m",et).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let r=sN(this,G,"m",eo).call(this);this._emit("content.done",{content:e.message.content,parsed:r?r.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},ei=function(){if(this.ended)throw new rC("stream has ended, this shouldn't happen");let e=sN(this,Z,"f");if(!e)throw new rC("request ended without sending any chunks");return sO(this,Z,void 0,"f"),sO(this,Q,[],"f"),function(e,t){var r;let{id:n,choices:s,created:i,model:o,system_fingerprint:a,...l}=e;return r={...l,id:n,choices:s.map(({message:t,finish_reason:r,index:n,logprobs:s,...i})=>{if(!r)throw new rC(`missing finish_reason for choice ${n}`);let{content:o=null,function_call:a,tool_calls:l,...c}=t,u=t.role;if(!u)throw new rC(`missing role for choice ${n}`);if(a){let{arguments:e,name:l}=a;if(null==e)throw new rC(`missing function_call.arguments for choice ${n}`);if(!l)throw new rC(`missing function_call.name for choice ${n}`);return{...i,message:{content:o,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:r,index:n,logprobs:s}}return l?{...i,index:n,finish_reason:r,logprobs:s,message:{...c,role:u,content:o,refusal:t.refusal??null,tool_calls:l.map((t,r)=>{let{function:s,type:i,id:o,...a}=t,{arguments:l,name:c,...u}=s||{};if(null==o)throw new rC(`missing choices[${n}].tool_calls[${r}].id
${s$(e)}`);if(null==i)throw new rC(`missing choices[${n}].tool_calls[${r}].type
${s$(e)}`);if(null==c)throw new rC(`missing choices[${n}].tool_calls[${r}].function.name
${s$(e)}`);if(null==l)throw new rC(`missing choices[${n}].tool_calls[${r}].function.arguments
${s$(e)}`);return{...a,id:o,type:i,function:{...u,name:c,arguments:l}}})}}:{...i,message:{...c,content:o,role:u,refusal:t.refusal??null},finish_reason:r,index:n,logprobs:s}}),created:i,model:o,object:"chat.completion",...a?{system_fingerprint:a}:{}},t&&sS(t)?sx(r,t):{...r,choices:r.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,sN(this,Y,"f"))},eo=function(){let e=sN(this,Y,"f")?.response_format;return sv(e)?e:null},ea=function(e){var t,r,n,s;let i=sN(this,Z,"f"),{choices:o,...a}=e;for(let{delta:o,finish_reason:l,index:c,logprobs:u=null,...d}of(i?Object.assign(i,a):i=sO(this,Z,{...a,choices:[]},"f"),e.choices)){let e=i.choices[c];if(e||(e=i.choices[c]={finish_reason:l,index:c,message:{},logprobs:u,...d}),u)if(e.logprobs){let{content:n,refusal:s,...i}=u;Object.assign(e.logprobs,i),n&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...n)),s&&((r=e.logprobs).refusal??(r.refusal=[]),e.logprobs.refusal.push(...s))}else e.logprobs=Object.assign({},u);if(l&&(e.finish_reason=l,sN(this,Y,"f")&&sS(sN(this,Y,"f")))){if("length"===l)throw new rF;if("content_filter"===l)throw new rW}if(Object.assign(e,d),!o)continue;let{content:a,refusal:h,function_call:f,role:p,tool_calls:m,...g}=o;if(Object.assign(e.message,g),h&&(e.message.refusal=(e.message.refusal||"")+h),p&&(e.message.role=p),f&&(e.message.function_call?(f.name&&(e.message.function_call.name=f.name),f.arguments&&((n=e.message.function_call).arguments??(n.arguments=""),e.message.function_call.arguments+=f.arguments)):e.message.function_call=f),a&&(e.message.content=(e.message.content||"")+a,!e.message.refusal&&sN(this,G,"m",eo).call(this)&&(e.message.parsed=sA(e.message.content))),m)for(let{index:t,id:r,type:n,function:i,...o}of(e.message.tool_calls||(e.message.tool_calls=[]),m)){let a=(s=e.message.tool_calls)[t]??(s[t]={});Object.assign(a,o),r&&(a.id=r),n&&(a.type=n),i&&(a.function??(a.function={name:i.name??"",arguments:""})),i?.name&&(a.function.name=i.name),i?.arguments&&(a.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let r=e.tools?.find(e=>e.function?.name===t.function.name);return sw(r)||r?.function.strict||!1}(sN(this,Y,"f"),a)&&(a.function.parsed_arguments=sA(a.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("chunk",r=>{let n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new rJ(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function s$(e){return JSON.stringify(e)}class sq extends sI{static fromReadableStream(e){let t=new sq(null);return t._run(()=>t._fromReadableStream(e)),t}static runFunctions(e,t,r){let n=new sq(null),s={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runFunctions"}};return n._run(()=>n._runFunctions(e,t,s)),n}static runTools(e,t,r){let n=new sq(t),s={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,s)),n}}class sD extends nN{parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new rC(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new rC(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"beta.chat.completions.parse"}})._thenUnwrap(t=>sx(t,e))}runFunctions(e,t){return e.stream?sq.runFunctions(this._client,e,t):sj.runFunctions(this._client,e,t)}runTools(e,t){return e.stream?sq.runTools(this._client,e,t):sj.runTools(this._client,e,t)}stream(e,t){return sI.createChatCompletion(this._client,e,t)}}class sL extends nN{constructor(){super(...arguments),this.completions=new sD(this._client)}}(sL||(sL={})).Completions=sD;class sB extends nN{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}}class sM extends nN{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}}class sF extends nN{constructor(){super(...arguments),this.sessions=new sB(this._client),this.transcriptionSessions=new sM(this._client)}}sF.Sessions=sB,sF.TranscriptionSessions=sM;var sW=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)},sz=function(e,t,r,n,s){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r};class sU extends s_{constructor(){super(...arguments),el.add(this),ec.set(this,[]),eu.set(this,{}),ed.set(this,{}),eh.set(this,void 0),ef.set(this,void 0),ep.set(this,void 0),em.set(this,void 0),eg.set(this,void 0),ey.set(this,void 0),eb.set(this,void 0),e_.set(this,void 0),ev.set(this,void 0)}[(ec=new WeakMap,eu=new WeakMap,ed=new WeakMap,eh=new WeakMap,ef=new WeakMap,ep=new WeakMap,em=new WeakMap,eg=new WeakMap,ey=new WeakMap,eb=new WeakMap,e_=new WeakMap,ev=new WeakMap,el=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",r=>{let n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new sU;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),this._connected();let n=rJ.fromReadableStream(e,this.controller);for await(let e of n)sW(this,el,"m",ew).call(this,e);if(n.controller.signal?.aborted)throw new rR;return this._addRun(sW(this,el,"m",ex).call(this))}toReadableStream(){return new rJ(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,r,n,s){let i=new sU;return i._run(()=>i._runToolAssistantStream(e,t,r,n,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),i}async _createToolAssistantStream(e,t,r,n,s){let i=s?.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort()));let o={...n,stream:!0},a=await e.submitToolOutputs(t,r,o,{...s,signal:this.controller.signal});for await(let e of(this._connected(),a))sW(this,el,"m",ew).call(this,e);if(a.controller.signal?.aborted)throw new rR;return this._addRun(sW(this,el,"m",ex).call(this))}static createThreadAssistantStream(e,t,r){let n=new sU;return n._run(()=>n._threadAssistantStream(e,t,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(e,t,r,n){let s=new sU;return s._run(()=>s._runAssistantStream(e,t,r,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),s}currentEvent(){return sW(this,eb,"f")}currentRun(){return sW(this,e_,"f")}currentMessageSnapshot(){return sW(this,eh,"f")}currentRunStepSnapshot(){return sW(this,ev,"f")}async finalRunSteps(){return await this.done(),Object.values(sW(this,eu,"f"))}async finalMessages(){return await this.done(),Object.values(sW(this,ed,"f"))}async finalRun(){if(await this.done(),!sW(this,ef,"f"))throw Error("Final run was not received.");return sW(this,ef,"f")}async _createThreadAssistantStream(e,t,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let s={...t,stream:!0},i=await e.createAndRun(s,{...r,signal:this.controller.signal});for await(let e of(this._connected(),i))sW(this,el,"m",ew).call(this,e);if(i.controller.signal?.aborted)throw new rR;return this._addRun(sW(this,el,"m",ex).call(this))}async _createAssistantStream(e,t,r,n){let s=n?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort()));let i={...r,stream:!0},o=await e.create(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),o))sW(this,el,"m",ew).call(this,e);if(o.controller.signal?.aborted)throw new rR;return this._addRun(sW(this,el,"m",ex).call(this))}static accumulateDelta(e,t){for(let[r,n]of Object.entries(t)){if(!e.hasOwnProperty(r)){e[r]=n;continue}let t=e[r];if(null==t||"index"===r||"type"===r){e[r]=n;continue}if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else if(nO(t)&&nO(n))t=this.accumulateDelta(t,n);else if(Array.isArray(t)&&Array.isArray(n)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...n);continue}for(let e of n){if(!nO(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let r=e.index;if(null==r)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof r)throw Error(`Expected array delta entry \`index\` property to be a number but got ${r}`);let n=t[r];null==n?t.push(e):t[r]=this.accumulateDelta(n,e)}continue}else throw Error(`Unhandled record type: ${r}, deltaValue: ${n}, accValue: ${t}`);e[r]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,r){return await this._createThreadAssistantStream(t,e,r)}async _runAssistantStream(e,t,r,n){return await this._createAssistantStream(t,e,r,n)}async _runToolAssistantStream(e,t,r,n,s){return await this._createToolAssistantStream(r,e,t,n,s)}}ew=function(e){if(!this.ended)switch(sz(this,eb,e,"f"),sW(this,el,"m",eT).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":sW(this,el,"m",eP).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":sW(this,el,"m",ek).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":sW(this,el,"m",eS).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},ex=function(){if(this.ended)throw new rC("stream has ended, this shouldn't happen");if(!sW(this,ef,"f"))throw Error("Final run has not been received");return sW(this,ef,"f")},eS=function(e){let[t,r]=sW(this,el,"m",eE).call(this,e,sW(this,eh,"f"));for(let e of(sz(this,eh,t,"f"),sW(this,ed,"f")[t.id]=t,r)){let r=t.content[e.index];r?.type=="text"&&this._emit("textCreated",r.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let r of e.data.delta.content){if("text"==r.type&&r.text){let e=r.text,n=t.content[r.index];if(n&&"text"==n.type)this._emit("textDelta",e,n.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(r.index!=sW(this,ep,"f")){if(sW(this,em,"f"))switch(sW(this,em,"f").type){case"text":this._emit("textDone",sW(this,em,"f").text,sW(this,eh,"f"));break;case"image_file":this._emit("imageFileDone",sW(this,em,"f").image_file,sW(this,eh,"f"))}sz(this,ep,r.index,"f")}sz(this,em,t.content[r.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==sW(this,ep,"f")){let t=e.data.content[sW(this,ep,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,sW(this,eh,"f"));break;case"text":this._emit("textDone",t.text,sW(this,eh,"f"))}}sW(this,eh,"f")&&this._emit("messageDone",e.data),sz(this,eh,void 0,"f")}},ek=function(e){let t=sW(this,el,"m",ej).call(this,e);switch(sz(this,ev,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let r=e.data.delta;if(r.step_details&&"tool_calls"==r.step_details.type&&r.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of r.step_details.tool_calls)e.index==sW(this,eg,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(sW(this,ey,"f")&&this._emit("toolCallDone",sW(this,ey,"f")),sz(this,eg,e.index,"f"),sz(this,ey,t.step_details.tool_calls[e.index],"f"),sW(this,ey,"f")&&this._emit("toolCallCreated",sW(this,ey,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":sz(this,ev,void 0,"f"),"tool_calls"==e.data.step_details.type&&sW(this,ey,"f")&&(this._emit("toolCallDone",sW(this,ey,"f")),sz(this,ey,void 0,"f")),this._emit("runStepDone",e.data,t)}},eT=function(e){sW(this,ec,"f").push(e),this._emit("event",e)},ej=function(e){switch(e.event){case"thread.run.step.created":return sW(this,eu,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=sW(this,eu,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let r=e.data;if(r.delta){let n=sU.accumulateDelta(t,r.delta);sW(this,eu,"f")[e.data.id]=n}return sW(this,eu,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":sW(this,eu,"f")[e.data.id]=e.data}if(sW(this,eu,"f")[e.data.id])return sW(this,eu,"f")[e.data.id];throw Error("No snapshot available")},eE=function(e,t){let r=[];switch(e.event){case"thread.message.created":return[e.data,r];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(let e of n.delta.content)if(e.index in t.content){let r=t.content[e.index];t.content[e.index]=sW(this,el,"m",eC).call(this,e,r)}else t.content[e.index]=e,r.push(e);return[t,r];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,r];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},eC=function(e,t){return sU.accumulateDelta(t,e)},eP=function(e){switch(sz(this,e_,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":sz(this,ef,e.data,"f"),sW(this,ey,"f")&&(this._emit("toolCallDone",sW(this,ey,"f")),sz(this,ey,void 0,"f"))}};class sH extends nN{create(e,t,r){return this._client.post(`/threads/${e}/messages`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}retrieve(e,t,r){return this._client.get(`/threads/${e}/messages/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}update(e,t,r,n){return this._client.post(`/threads/${e}/messages/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}list(e,t={},r){return nu(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/messages`,sV,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}del(e,t,r){return this._client.delete(`/threads/${e}/messages/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}}class sV extends nD{}sH.MessagesPage=sV;class sJ extends nN{retrieve(e,t,r,n={},s){return nu(n)?this.retrieve(e,t,r,{},n):this._client.get(`/threads/${e}/runs/${t}/steps/${r}`,{query:n,...s,headers:{"OpenAI-Beta":"assistants=v2",...s?.headers}})}list(e,t,r={},n){return nu(r)?this.list(e,t,{},r):this._client.getAPIList(`/threads/${e}/runs/${t}/steps`,sK,{query:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}}class sK extends nD{}sJ.RunStepsPage=sK;class sX extends nN{constructor(){super(...arguments),this.steps=new sJ(this._client)}create(e,t,r){let{include:n,...s}=t;return this._client.post(`/threads/${e}/runs`,{query:{include:n},body:s,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers},stream:t.stream??!1})}retrieve(e,t,r){return this._client.get(`/threads/${e}/runs/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}update(e,t,r,n){return this._client.post(`/threads/${e}/runs/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers}})}list(e,t={},r){return nu(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/runs`,sG,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}cancel(e,t,r){return this._client.post(`/threads/${e}/runs/${t}/cancel`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}async createAndPoll(e,t,r){let n=await this.create(e,t,r);return await this.poll(e,n.id,r)}createAndStream(e,t,r){return sU.createAssistantStream(e,this._client.beta.threads.runs,t,r)}async poll(e,t,r){let n={...r?.headers,"X-Stainless-Poll-Helper":"true"};for(r?.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){let{data:s,response:i}=await this.retrieve(e,t,{...r,headers:{...r?.headers,...n}}).withResponse();switch(s.status){case"queued":case"in_progress":case"cancelling":let o=5e3;if(r?.pollIntervalMs)o=r.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(o=t)}}await nb(o);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return s}}}stream(e,t,r){return sU.createAssistantStream(e,this._client.beta.threads.runs,t,r)}submitToolOutputs(e,t,r,n){return this._client.post(`/threads/${e}/runs/${t}/submit_tool_outputs`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n?.headers},stream:r.stream??!1})}async submitToolOutputsAndPoll(e,t,r,n){let s=await this.submitToolOutputs(e,t,r,n);return await this.poll(e,s.id,n)}submitToolOutputsStream(e,t,r,n){return sU.createToolAssistantStream(e,t,this._client.beta.threads.runs,r,n)}}class sG extends nD{}sX.RunsPage=sG,sX.Steps=sJ,sX.RunStepsPage=sK;class sY extends nN{constructor(){super(...arguments),this.runs=new sX(this._client),this.messages=new sH(this._client)}create(e={},t){return nu(e)?this.create({},e):this._client.post("/threads",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}retrieve(e,t){return this._client.get(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}update(e,t,r){return this._client.post(`/threads/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r?.headers}})}del(e,t){return this._client.delete(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers}})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t?.headers},stream:e.stream??!1})}async createAndRunPoll(e,t){let r=await this.createAndRun(e,t);return await this.runs.poll(r.thread_id,r.id,t)}createAndRunStream(e,t){return sU.createThreadAssistantStream(e,this._client.beta.threads,t)}}sY.Runs=sX,sY.RunsPage=sG,sY.Messages=sH,sY.MessagesPage=sV;class sQ extends nN{constructor(){super(...arguments),this.realtime=new sF(this._client),this.chat=new sL(this._client),this.assistants=new sd(this._client),this.threads=new sY(this._client)}}sQ.Realtime=sF,sQ.Assistants=sd,sQ.AssistantsPage=sh,sQ.Threads=sY;class sZ extends nN{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(`/batches/${e}`,t)}list(e={},t){return nu(e)?this.list({},e):this._client.getAPIList("/batches",s0,{query:e,...t})}cancel(e,t){return this._client.post(`/batches/${e}/cancel`,t)}}class s0 extends nD{}sZ.BatchesPage=s0;class s1 extends nN{create(e,t,r){return this._client.post(`/uploads/${e}/parts`,r8({body:t,...r}))}}class s2 extends nN{constructor(){super(...arguments),this.parts=new s1(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(`/uploads/${e}/cancel`,t)}complete(e,t,r){return this._client.post(`/uploads/${e}/complete`,{body:t,...r})}}function s3(e,t){let r=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let r=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(r)?r.$parseRaw(t.arguments):r?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let r=e.content.map(e=>{var r,n;return"output_text"===e.type?{...e,parsed:(r=t,n=e.text,r.text?.format?.type!=="json_schema"?null:"$parseRaw"in r.text?.format?(r.text?.format).$parseRaw(n):JSON.parse(n))}:e});return{...e,content:r}}return e}),n=Object.assign({},e,{output:r});return Object.getOwnPropertyDescriptor(e,"output_text")||s4(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(let e of n.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),n}s2.Parts=s1;function s4(e){let t=[];for(let r of e.output)if("message"===r.type)for(let e of r.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class s6 extends nN{list(e,t={},r){return nu(t)?this.list(e,{},t):this._client.getAPIList(`/responses/${e}/input_items`,ie,{query:t,...r})}}var s8=function(e,t,r,n,s){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},s5=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class s9 extends s_{constructor(e){super(),eR.add(this),eA.set(this,void 0),eO.set(this,void 0),eN.set(this,void 0),s8(this,eA,e,"f")}static createResponse(e,t,r){let n=new s9(t);return n._run(()=>n._createOrRetrieveResponse(e,t,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createOrRetrieveResponse(e,t,r){let n,s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),s5(this,eR,"m",eI).call(this);let i=null;for await(let s of("response_id"in t?(n=await e.responses.retrieve(t.response_id,{stream:!0},{...r,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):n=await e.responses.create({...t,stream:!0},{...r,signal:this.controller.signal}),this._connected(),n))s5(this,eR,"m",e$).call(this,s,i);if(n.controller.signal?.aborted)throw new rR;return s5(this,eR,"m",eq).call(this)}[(eA=new WeakMap,eO=new WeakMap,eN=new WeakMap,eR=new WeakSet,eI=function(){this.ended||s8(this,eO,void 0,"f")},e$=function(e,t){if(this.ended)return;let r=(e,r)=>{(null==t||r.sequence_number>t)&&this._emit(e,r)},n=s5(this,eR,"m",eD).call(this,e);switch(r("event",e),e.type){case"response.output_text.delta":{let t=n.output[e.output_index];if(!t)throw new rC(`missing output at index ${e.output_index}`);if("message"===t.type){let n=t.content[e.content_index];if(!n)throw new rC(`missing content at index ${e.content_index}`);if("output_text"!==n.type)throw new rC(`expected content to be 'output_text', got ${n.type}`);r("response.output_text.delta",{...e,snapshot:n.text})}break}case"response.function_call_arguments.delta":{let t=n.output[e.output_index];if(!t)throw new rC(`missing output at index ${e.output_index}`);"function_call"===t.type&&r("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:r(e.type,e)}},eq=function(){if(this.ended)throw new rC("stream has ended, this shouldn't happen");let e=s5(this,eO,"f");if(!e)throw new rC("request ended without sending any events");s8(this,eO,void 0,"f");let t=function(e,t){var r;return t&&(r=t,sv(r.text?.format))?s3(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,s5(this,eA,"f"));return s8(this,eN,t,"f"),t},eD=function(e){let t=s5(this,eO,"f");if(!t){if("response.created"!==e.type)throw new rC(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return s8(this,eO,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let r=t.output[e.output_index];if(!r)throw new rC(`missing output at index ${e.output_index}`);"message"===r.type&&r.content.push(e.part);break}case"response.output_text.delta":{let r=t.output[e.output_index];if(!r)throw new rC(`missing output at index ${e.output_index}`);if("message"===r.type){let t=r.content[e.content_index];if(!t)throw new rC(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new rC(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let r=t.output[e.output_index];if(!r)throw new rC(`missing output at index ${e.output_index}`);"function_call"===r.type&&(r.arguments+=e.delta);break}case"response.completed":s8(this,eO,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",r=>{let n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=s5(this,eN,"f");if(!e)throw new rC("stream ended without producing a ChatCompletion");return e}}class s7 extends nN{constructor(){super(...arguments),this.inputItems=new s6(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&s4(e),e))}retrieve(e,t={},r){return this._client.get(`/responses/${e}`,{query:t,...r,stream:t?.stream??!1})}del(e,t){return this._client.delete(`/responses/${e}`,{...t,headers:{Accept:"*/*",...t?.headers}})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>s3(t,e))}stream(e,t){return s9.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(`/responses/${e}/cancel`,{...t,headers:{Accept:"*/*",...t?.headers}})}}class ie extends nD{}s7.InputItems=s6;class it extends nN{retrieve(e,t,r,n){return this._client.get(`/evals/${e}/runs/${t}/output_items/${r}`,n)}list(e,t,r={},n){return nu(r)?this.list(e,t,{},r):this._client.getAPIList(`/evals/${e}/runs/${t}/output_items`,ir,{query:r,...n})}}class ir extends nD{}it.OutputItemListResponsesPage=ir;class is extends nN{constructor(){super(...arguments),this.outputItems=new it(this._client)}create(e,t,r){return this._client.post(`/evals/${e}/runs`,{body:t,...r})}retrieve(e,t,r){return this._client.get(`/evals/${e}/runs/${t}`,r)}list(e,t={},r){return nu(t)?this.list(e,{},t):this._client.getAPIList(`/evals/${e}/runs`,ii,{query:t,...r})}del(e,t,r){return this._client.delete(`/evals/${e}/runs/${t}`,r)}cancel(e,t,r){return this._client.post(`/evals/${e}/runs/${t}`,r)}}class ii extends nD{}is.RunListResponsesPage=ii,is.OutputItems=it,is.OutputItemListResponsesPage=ir;class io extends nN{constructor(){super(...arguments),this.runs=new is(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(`/evals/${e}`,t)}update(e,t,r){return this._client.post(`/evals/${e}`,{body:t,...r})}list(e={},t){return nu(e)?this.list({},e):this._client.getAPIList("/evals",ia,{query:e,...t})}del(e,t){return this._client.delete(`/evals/${e}`,t)}}class ia extends nD{}io.EvalListResponsesPage=ia,io.Runs=is,io.RunListResponsesPage=ii;class il extends nN{retrieve(e,t,r){return this._client.get(`/containers/${e}/files/${t}/content`,{...r,headers:{Accept:"application/binary",...r?.headers},__binaryResponse:!0})}}class ic extends nN{constructor(){super(...arguments),this.content=new il(this._client)}create(e,t,r){return this._client.post(`/containers/${e}/files`,r8({body:t,...r}))}retrieve(e,t,r){return this._client.get(`/containers/${e}/files/${t}`,r)}list(e,t={},r){return nu(t)?this.list(e,{},t):this._client.getAPIList(`/containers/${e}/files`,iu,{query:t,...r})}del(e,t,r){return this._client.delete(`/containers/${e}/files/${t}`,{...r,headers:{Accept:"*/*",...r?.headers}})}}class iu extends nD{}ic.FileListResponsesPage=iu,ic.Content=il;class id extends nN{constructor(){super(...arguments),this.files=new ic(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(`/containers/${e}`,t)}list(e={},t){return nu(e)?this.list({},e):this._client.getAPIList("/containers",ih,{query:e,...t})}del(e,t){return this._client.delete(`/containers/${e}`,{...t,headers:{Accept:"*/*",...t?.headers}})}}class ih extends nD{}id.ContainerListResponsesPage=ih,id.Files=ic,id.FileListResponsesPage=iu;class ip extends ni{constructor({baseURL:e=nw("OPENAI_BASE_URL"),apiKey:t=nw("OPENAI_API_KEY"),organization:r=nw("OPENAI_ORG_ID")??null,project:n=nw("OPENAI_PROJECT_ID")??null,...s}={}){if(void 0===t)throw new rC("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let i={apiKey:t,organization:r,project:n,...s,baseURL:e||"https://api.openai.com/v1"};if(!i.dangerouslyAllowBrowser&&nC())throw new rC("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");super({baseURL:i.baseURL,timeout:i.timeout??6e5,httpAgent:i.httpAgent,maxRetries:i.maxRetries,fetch:i.fetch}),this.completions=new nI(this),this.chat=new nF(this),this.embeddings=new nW(this),this.files=new nz(this),this.images=new nH(this),this.audio=new nX(this),this.moderations=new nG(this),this.models=new nY(this),this.fineTuning=new se(this),this.graders=new sr(this),this.vectorStores=new sl(this),this.beta=new sQ(this),this.batches=new sZ(this),this.uploads=new s2(this),this.responses=new s7(this),this.evals=new io(this),this.containers=new id(this),this._options=i,this.apiKey=t,this.organization=r,this.project=n}defaultQuery(){return this._options.defaultQuery}defaultHeaders(e){return{...super.defaultHeaders(e),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project,...this._options.defaultHeaders}}authHeaders(e){return{Authorization:`Bearer ${this.apiKey}`}}stringifyQuery(e){return function(e,t={}){let r,n,s=e,i=function(e=to){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let r=e.charset||to.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=e6;if(void 0!==e.format){if(!te.call(e8,e.format))throw TypeError("Unknown format option provided.");n=e.format}let s=e8[n],i=to.filter;if(("function"==typeof e.filter||tr(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in tt?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":to.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let o=void 0===e.allowDots?!0==!!e.encodeDotInKeys||to.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:to.addQueryPrefix,allowDots:o,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:to.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:to.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?to.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:to.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:to.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:to.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:to.encodeValuesOnly,filter:i,format:n,formatter:s,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:to.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:to.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:to.strictNullHandling}}(t);"function"==typeof i.filter?s=(0,i.filter)("",s):tr(i.filter)&&(r=i.filter);let o=[];if("object"!=typeof s||null===s)return"";let a=tt[i.arrayFormat],l="comma"===a&&i.commaRoundTrip;r||(r=Object.keys(s)),i.sort&&r.sort(i.sort);let c=new WeakMap;for(let e=0;e<r.length;++e){let t=r[e];i.skipNulls&&null===s[t]||ts(o,function e(t,r,n,s,i,o,a,l,c,u,d,h,f,p,m,g,y,b){var _,v;let w,x=t,S=b,k=0,T=!1;for(;void 0!==(S=S.get(ta))&&!T;){let e=S.get(t);if(k+=1,void 0!==e)if(e===k)throw RangeError("Cyclic object value");else T=!0;void 0===S.get(ta)&&(k=0)}if("function"==typeof u?x=u(r,x):x instanceof Date?x=f?.(x):"comma"===n&&tr(x)&&(x=e7(x,function(e){return e instanceof Date?f?.(e):e})),null===x){if(o)return c&&!g?c(r,to.encoder,y,"key",p):r;x=""}if("string"==typeof(_=x)||"number"==typeof _||"boolean"==typeof _||"symbol"==typeof _||"bigint"==typeof _||(v=x)&&"object"==typeof v&&v.constructor&&v.constructor.isBuffer&&v.constructor.isBuffer(v)){if(c){let e=g?r:c(r,to.encoder,y,"key",p);return[m?.(e)+"="+m?.(c(x,to.encoder,y,"value",p))]}return[m?.(r)+"="+m?.(String(x))]}let j=[];if(void 0===x)return j;if("comma"===n&&tr(x))g&&c&&(x=e7(x,c)),w=[{value:x.length>0?x.join(",")||null:void 0}];else if(tr(u))w=u;else{let e=Object.keys(x);w=d?e.sort(d):e}let E=l?String(r).replace(/\./g,"%2E"):String(r),C=s&&tr(x)&&1===x.length?E+"[]":E;if(i&&tr(x)&&0===x.length)return C+"[]";for(let r=0;r<w.length;++r){let _=w[r],v="object"==typeof _&&void 0!==_.value?_.value:x[_];if(a&&null===v)continue;let S=h&&l?_.replace(/\./g,"%2E"):_,T=tr(x)?"function"==typeof n?n(C,S):C:C+(h?"."+S:"["+S+"]");b.set(t,k);let E=new WeakMap;E.set(ta,b),ts(j,e(v,T,n,s,i,o,a,l,"comma"===n&&g&&tr(x)?null:c,u,d,h,f,p,m,g,y,E))}return j}(s[t],t,a,l,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}let u=o.join(i.delimiter),d=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?d+="utf8=%26%2310003%3B&":d+="utf8=%E2%9C%93&"),u.length>0?d+u:""}(e,{arrayFormat:"brackets"})}}ip.OpenAI=ip,ip.DEFAULT_TIMEOUT=6e5,ip.OpenAIError=rC,ip.APIError=rP,ip.APIConnectionError=rA,ip.APIConnectionTimeoutError=rO,ip.APIUserAbortError=rR,ip.NotFoundError=rq,ip.ConflictError=rD,ip.RateLimitError=rB,ip.BadRequestError=rN,ip.AuthenticationError=rI,ip.InternalServerError=rM,ip.PermissionDeniedError=r$,ip.UnprocessableEntityError=rL,ip.toFile=r1,ip.fileFromPath=h,ip.Completions=nI,ip.Chat=nF,ip.ChatCompletionsPage=nB,ip.Embeddings=nW,ip.Files=nz,ip.FileObjectsPage=nU,ip.Images=nH,ip.Audio=nX,ip.Moderations=nG,ip.Models=nY,ip.ModelsPage=nQ,ip.FineTuning=se,ip.Graders=sr,ip.VectorStores=sl,ip.VectorStoresPage=sc,ip.VectorStoreSearchResponsesPage=su,ip.Beta=sQ,ip.Batches=sZ,ip.BatchesPage=s0,ip.Uploads=s2,ip.Responses=s7,ip.Evals=io,ip.EvalListResponsesPage=ia,ip.Containers=id,ip.ContainerListResponsesPage=ih;class im{constructor(){this.supabase=(0,eF.UU)(),this.openai=new ip({apiKey:process.env.OPENAI_API_KEY||""})}async analyzeUserSituation(e){try{let[t,r,n,s,i]=await Promise.all([this.getUserProfile(e),this.getUserDocuments(e),this.getUserPolicies(e),this.getUserClaims(e),this.getChatHistory(e)]),[o,a,l,c]=await Promise.all([this.detectCoverageGaps(t,r,n),this.identifyCostOptimizations(n,t),this.checkRenewalDates(n),this.identifyClaimOpportunities(i,r)]);return{coverageGaps:o,costOptimizations:a,renewalReminders:l,claimOpportunities:c,riskAlerts:[],marketOpportunities:[]}}catch(e){return console.error("Error analyzing user situation:",e),{coverageGaps:[],costOptimizations:[],renewalReminders:[],claimOpportunities:[],riskAlerts:[],marketOpportunities:[]}}}async detectCoverageGaps(e,t,r){let n=[];if(!e)return n;let s=r.map(e=>e.policy_type.toLowerCase()),i=e.date_of_birth?new Date().getFullYear()-new Date(e.date_of_birth).getFullYear():0;return i>=18&&!s.includes("ansvarsforsikring")&&n.push({type:"ansvarsforsikring",severity:"high",description:"Du mangler en privat ansvarsforsikring, som er essentiel for alle voksne.",recommendation:"Tegn en privat ansvarsforsikring hurtigst muligt. Det er billigt og d\xe6kker store \xf8konomiske risici.",estimatedCost:1200}),i>=25&&!s.includes("indboforsikring")&&n.push({type:"indboforsikring",severity:"medium",description:"En indboforsikring beskytter dine ejendele mod tyveri, brand og vandskade.",recommendation:"Overvej en indboforsikring, is\xe6r hvis du bor i eget hjem eller har v\xe6rdifulde ejendele.",estimatedCost:2400}),e.address&&("house"!==e.address.type||s.includes("husejerforsikring")||n.push({type:"husejerforsikring",severity:"high",description:"Som husejer har du brug for en husejerforsikring til bygningen.",recommendation:"Tegn husejerforsikring omg\xe5ende. Det er ofte et krav fra realkreditl\xe5net.",estimatedCost:8e3})),n}async identifyCostOptimizations(e,t){let r=[];for(let n of e){if(n.deductible_amount&&n.premium_amount){let e=n.deductible_amount,t=n.premium_amount;if(e<5e3&&t>3e3){let s=.15*t;r.push({type:"deductible",currentCost:t,potentialSaving:s,description:`Ved at \xf8ge selvrisikoen p\xe5 din ${n.policy_type} fra ${e} kr til 5.000 kr kan du spare p\xe5 pr\xe6mien.`,actionRequired:"Kontakt dit forsikringsselskab for at justere selvrisikoen",riskLevel:"low"})}}if(t?.date_of_birth&&n.premium_amount){let e=new Date().getFullYear()-new Date(t.date_of_birth).getFullYear();"bilforsikring"===n.policy_type&&e>25&&n.premium_amount>8e3&&r.push({type:"provider",currentCost:n.premium_amount,potentialSaving:.2*n.premium_amount,description:"Din bilforsikring virker dyr for din alder. Unge f\xf8rere kan ofte f\xe5 bedre priser andre steder.",actionRequired:"Indhent tilbud fra andre selskaber",riskLevel:"low"})}}if(e.length>=2){let t=e.reduce((e,t)=>e+(t.premium_amount||0),0);new Set(e.map(e=>e.company_id).filter(Boolean)).size>1&&r.push({type:"bundling",currentCost:t,potentialSaving:.1*t,description:"Du har forsikringer hos flere selskaber. Ved at samle dem kan du f\xe5 paketrabat.",actionRequired:"F\xe5 tilbud p\xe5 samling af forsikringer",riskLevel:"low"})}return r}async checkRenewalDates(e){let t=[],r=new Date;for(let n of e)if(n.end_date){let e=Math.ceil((new Date(n.end_date).getTime()-r.getTime())/864e5);if(e<=60&&e>0){let r="low",s="";e<=14?(r="high",s="HASTER: Forny eller skift forsikring NU for at undg\xe5 d\xe6kningshuller!"):e<=30?(r="medium",s="Tid til at handle: Sammenlign priser og forny din forsikring."):(r="low",s="Tid til planl\xe6gning: Unders\xf8g muligheder for fornyelse eller skift."),t.push({policyId:n.id,policyType:n.policy_type,expirationDate:n.end_date,daysUntilExpiration:e,priority:r,recommendation:s})}}return t.sort((e,t)=>e.daysUntilExpiration-t.daysUntilExpiration)}async identifyClaimOpportunities(e,t){let r=[],n={vandskade:["vand","l\xe6k","oversv\xf8mmelse","r\xf8r","taget"],tyveri:["stj\xe5let","tyveri","indbrud","mangler"],skade:["\xf8delagt","beskadiget","knust","ridset"],brand:["brand","ild","r\xf8g","ildebrand"]};for(let t of e)if("user"===t.role){let e=t.content.toLowerCase();for(let[t,s]of Object.entries(n)){let n=s.filter(t=>e.includes(t)).length;n>=2&&r.push({incidentType:t,confidence:Math.min(.9,.3*n),description:`Baseret p\xe5 din beskrivelse lyder det som en ${t} der kan v\xe6re d\xe6kket af forsikring.`,estimatedValue:this.estimateClaimValue(t),timeLimit:this.getClaimTimeLimit(t),requiredDocuments:this.getRequiredDocuments(t)})}}return r}estimateClaimValue(e){return({vandskade:25e3,tyveri:15e3,skade:8e3,brand:1e5})[e]||1e4}getClaimTimeLimit(e){return({vandskade:"48 timer for akutte skader",tyveri:"24 timer til politianmeldelse",skade:"14 dage for anmeldelse",brand:"\xd8jeblikkeligt"})[e]||"14 dage"}getRequiredDocuments(e){return({vandskade:["Billeder af skaden","H\xe5ndv\xe6rker rapport","Forsikringspolice"],tyveri:["Politianmeldelse","Liste over stj\xe5lne ting","Kvitteringer"],skade:["Billeder","Reparationsestimater","Ulykkesrapport"],brand:["Brandrapport","Billeder","V\xe6rditaksering"]})[e]||["Forsikringspolice","Dokumentation af skade"]}async generateProactiveRecommendations(e){let t=[];for(let r of e.coverageGaps)t.push({insight_type:"coverage_gap",title:`Manglende ${r.type}`,description:r.description,priority:r.severity,recommended_actions:[r.recommendation],confidence_score:.9,data_sources:{analysis:"coverage_analysis"}});for(let r of e.costOptimizations)t.push({insight_type:"cost_optimization",title:`Spar ${Math.round(r.potentialSaving)} kr \xe5rligt`,description:r.description,priority:r.potentialSaving>2e3?"high":"medium",recommended_actions:[r.actionRequired],confidence_score:.8,data_sources:{analysis:"cost_analysis",saving:r.potentialSaving}});for(let r of e.renewalReminders)t.push({insight_type:"renewal_reminder",title:`${r.policyType} udl\xf8ber om ${r.daysUntilExpiration} dage`,description:r.recommendation,priority:r.priority,recommended_actions:["Sammenlign priser","Forny forsikring"],confidence_score:1,data_sources:{policyId:r.policyId},expires_at:new Date(r.expirationDate).toISOString()});for(let r of e.claimOpportunities)t.push({insight_type:"claim_opportunity",title:`Potentiel ${r.incidentType} skade`,description:r.description,priority:"high",recommended_actions:[`Anmeld til forsikring inden ${r.timeLimit}`],confidence_score:r.confidence,data_sources:{incidentType:r.incidentType,estimatedValue:r.estimatedValue}});return t}async getUserProfile(e){let{data:t}=await this.supabase.from("user_profiles").select("*").eq("id",e).single();return t}async getUserDocuments(e){let{data:t}=await this.supabase.from("documents").select("*").eq("user_id",e).eq("is_active",!0);return t||[]}async getUserPolicies(e){let{data:t}=await this.supabase.from("user_policies").select("*").eq("user_id",e).eq("status","active");return t||[]}async getUserClaims(e){let{data:t}=await this.supabase.from("claims").select("*").eq("user_id",e);return t||[]}async getChatHistory(e){let{data:t}=await this.supabase.from("chat_messages").select("*").eq("user_id",e).order("created_at",{ascending:!1}).limit(50);return t||[]}async saveInsights(e,t){try{let{error:r}=await this.supabase.from("ai_insights").insert(t.map(t=>({...t,user_id:e,created_at:new Date().toISOString()})));r&&console.error("Error saving insights:",r)}catch(e){console.error("Error saving insights:",e)}}async getUserInsights(e){let{data:t}=await this.supabase.from("ai_insights").select("*").eq("user_id",e).eq("is_acknowledged",!1).order("priority",{ascending:!1}).order("created_at",{ascending:!1});return t||[]}}function ig({insight:e,onAcknowledge:t}){return(0,eL.jsxs)("div",{className:`border rounded-lg p-4 ${(e=>{switch(e){case"high":return"border-red-200 bg-red-50";case"medium":return"border-yellow-200 bg-yellow-50";case"urgent":return"border-red-300 bg-red-100";default:return"border-blue-200 bg-blue-50"}})(e.priority)}`,children:[(0,eL.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,eL.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,eL.jsx)("span",{className:"text-xl",children:(e=>{switch(e){case"coverage_gap":return"\uD83D\uDEE1️";case"cost_optimization":return"\uD83D\uDCB0";case"renewal_reminder":return"\uD83D\uDCC5";case"claim_opportunity":return"\uD83D\uDCDD";case"risk_alert":return"⚠️";default:return"\uD83D\uDCA1"}})(e.insight_type)}),(0,eL.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"high":return"\uD83D\uDD34";case"medium":return"\uD83D\uDFE1";case"urgent":return"\uD83D\uDEA8";default:return"\uD83D\uDD35"}})(e.priority)}),(0,eL.jsx)("h3",{className:"font-semibold text-gray-900",children:e.title})]}),!e.is_acknowledged&&(0,eL.jsx)("button",{onClick:()=>t(e.id),className:"text-sm text-gray-500 hover:text-gray-700",children:"✓ Mark\xe9r som l\xe6st"})]}),(0,eL.jsx)("p",{className:"text-gray-700 mb-4",children:e.description}),e.recommended_actions&&Array.isArray(e.recommended_actions)&&(0,eL.jsxs)("div",{className:"mb-4",children:[(0,eL.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Anbefalede handlinger:"}),(0,eL.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:e.recommended_actions.map((e,t)=>(0,eL.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,eL.jsx)("span",{children:"•"}),(0,eL.jsx)("span",{children:e})]},t))})]}),(0,eL.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,eL.jsxs)("span",{children:["Sikkerhed: ",Math.round(100*(e.confidence_score||0)),"%"]}),(0,eL.jsx)("span",{children:new Date(e.created_at).toLocaleDateString("da-DK")})]})]})}function iy(){let{insights:e,user:t,acknowledgeInsight:r,addInsight:n,setLoading:s,isLoading:i}=e4(),[o,a]=(0,eB.useState)(!1),[l,c]=(0,eB.useState)("unread"),u=new im,d=async()=>{if(t){a(!0);try{let e=await u.analyzeUserSituation(t.id),r=await u.generateProactiveRecommendations(e);for(let e of r)n(e);await u.saveInsights(t.id,r)}catch(e){console.error("Error refreshing insights:",e)}finally{a(!1)}}},h=async e=>{r(e)},f=e.filter(e=>{switch(l){case"unread":return!e.is_acknowledged;case"high_priority":return"high"===e.priority||"urgent"===e.priority;default:return!0}}),p={total:e.length,unread:e.filter(e=>!e.is_acknowledged).length,highPriority:e.filter(e=>"high"===e.priority||"urgent"===e.priority).length,costSavings:e.filter(e=>"cost_optimization"===e.insight_type).reduce((e,t)=>e+(t.data_sources?.saving||0),0)};return i?(0,eL.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,eL.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-insurance-blue"})}):(0,eL.jsxs)("div",{className:"space-y-6",children:[(0,eL.jsxs)("div",{className:"flex items-center justify-between",children:[(0,eL.jsxs)("div",{children:[(0,eL.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"AI Indsigter"}),(0,eL.jsx)("p",{className:"text-gray-600",children:"Personaliserede anbefalinger baseret p\xe5 din forsikringssituation"})]}),(0,eL.jsx)("button",{onClick:d,disabled:o,className:"btn-primary disabled:opacity-50",children:o?(0,eL.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,eL.jsxs)("svg",{className:"animate-spin h-4 w-4",viewBox:"0 0 24 24",children:[(0,eL.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,eL.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,eL.jsx)("span",{children:"Opdaterer..."})]}):"Opdater indsigter"})]}),(0,eL.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,eL.jsxs)("div",{className:"bg-white rounded-lg border p-4",children:[(0,eL.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:p.total}),(0,eL.jsx)("div",{className:"text-sm text-gray-600",children:"Totale indsigter"})]}),(0,eL.jsxs)("div",{className:"bg-white rounded-lg border p-4",children:[(0,eL.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:p.unread}),(0,eL.jsx)("div",{className:"text-sm text-gray-600",children:"Ul\xe6ste"})]}),(0,eL.jsxs)("div",{className:"bg-white rounded-lg border p-4",children:[(0,eL.jsx)("div",{className:"text-2xl font-bold text-red-600",children:p.highPriority}),(0,eL.jsx)("div",{className:"text-sm text-gray-600",children:"H\xf8j prioritet"})]}),(0,eL.jsxs)("div",{className:"bg-white rounded-lg border p-4",children:[(0,eL.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[Math.round(p.costSavings).toLocaleString()," kr"]}),(0,eL.jsx)("div",{className:"text-sm text-gray-600",children:"Potentielle besparelser"})]})]}),(0,eL.jsxs)("div",{className:"flex space-x-2",children:[(0,eL.jsxs)("button",{onClick:()=>c("all"),className:`px-4 py-2 rounded-lg text-sm font-medium ${"all"===l?"bg-insurance-blue text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["Alle (",p.total,")"]}),(0,eL.jsxs)("button",{onClick:()=>c("unread"),className:`px-4 py-2 rounded-lg text-sm font-medium ${"unread"===l?"bg-insurance-blue text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["Ul\xe6ste (",p.unread,")"]}),(0,eL.jsxs)("button",{onClick:()=>c("high_priority"),className:`px-4 py-2 rounded-lg text-sm font-medium ${"high_priority"===l?"bg-insurance-blue text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["H\xf8j prioritet (",p.highPriority,")"]})]}),0===f.length?(0,eL.jsxs)("div",{className:"text-center py-12",children:[(0,eL.jsx)("div",{className:"text-4xl mb-4",children:"\uD83C\uDF89"}),(0,eL.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"unread"===l?"Ingen ul\xe6ste indsigter":"Ingen indsigter"}),(0,eL.jsx)("p",{className:"text-gray-600",children:"unread"===l?"Du har l\xe6st alle dine indsigter. Godt arbejde!":'Klik p\xe5 "Opdater indsigter" for at f\xe5 AI-genererede anbefalinger.'})]}):(0,eL.jsx)("div",{className:"space-y-4",children:f.map(e=>(0,eL.jsx)(ig,{insight:e,onAcknowledge:h},e.id))})]})}function ib(){let{user:e,loading:t}=(0,eM.A)(),[r,n]=(0,eB.useState)(!1),[s,i]=(0,eB.useState)("chat"),{messages:o,documents:a,selectedDocument:l,addMessage:c,addDocument:u,selectDocument:d,insights:h}=e4(),f=async t=>{c({role:"user",content:t,id:Date.now().toString(),conversation_id:"default",user_id:e?.id||"anonymous",extracted_entities:[],document_references:[],ai_metadata:{},is_flagged:!1,created_at:new Date().toISOString()});try{let r=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:t,documents:a.map(e=>({name:e.filename,content:e.content,type:e.document_type})),conversationHistory:o,userId:e?.id})});if(!r.ok)throw Error("API request failed");let n=await r.json(),s={role:"assistant",content:n.response,id:(Date.now()+1).toString(),conversation_id:"default",user_id:e?.id||"anonymous",extracted_entities:[],document_references:[],ai_metadata:n.metadata||{},is_flagged:!1,created_at:new Date().toISOString()};c(s)}catch(t){console.error("Error sending message:",t),c({role:"assistant",content:"Beklager, der opstod en fejl. Pr\xf8v venligst igen.",id:(Date.now()+1).toString(),conversation_id:"default",user_id:e?.id||"anonymous",extracted_entities:[],document_references:[],ai_metadata:{},is_flagged:!1,created_at:new Date().toISOString()})}},p=e=>{u(e)};return t?(0,eL.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,eL.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-insurance-blue"})}):e?(0,eL.jsxs)(eL.Fragment,{children:[(0,eL.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,eL.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,eL.jsxs)("div",{className:"flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm border w-fit",children:[(0,eL.jsx)("button",{onClick:()=>i("chat"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"chat"===s?"bg-insurance-blue text-white":"text-gray-600 hover:text-gray-900"}`,children:"\uD83D\uDCAC Chat"}),(0,eL.jsxs)("button",{onClick:()=>i("dashboard"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"dashboard"===s?"bg-insurance-blue text-white":"text-gray-600 hover:text-gray-900"}`,children:["\uD83D\uDCCA Dashboard",h.filter(e=>!e.is_acknowledged).length>0&&(0,eL.jsx)("span",{className:"ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1",children:h.filter(e=>!e.is_acknowledged).length})]}),(0,eL.jsxs)("button",{onClick:()=>i("documents"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"documents"===s?"bg-insurance-blue text-white":"text-gray-600 hover:text-gray-900"}`,children:["\uD83D\uDCC1 Dokumenter (",a.length,")"]})]}),"dashboard"===s&&(0,eL.jsx)(iy,{}),"documents"===s&&(0,eL.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,eL.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,eL.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Upload Nyt Dokument"}),(0,eL.jsx)(Object(function(){var e=Error("Cannot find module '@/components/upload/PDFUpload'");throw e.code="MODULE_NOT_FOUND",e}()),{onDocumentUpload:p})]}),(0,eL.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,eL.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Dine Dokumenter"}),0===a.length?(0,eL.jsx)("p",{className:"text-gray-500 text-center py-8",children:"Ingen dokumenter uploadet endnu"}):(0,eL.jsx)("div",{className:"space-y-2",children:a.map(e=>(0,eL.jsxs)("button",{onClick:()=>d(e),className:`w-full text-left p-3 rounded-lg border-2 transition-colors ${l?.id===e.id?"border-insurance-blue bg-blue-50":"border-gray-200 hover:border-gray-300"}`,children:[(0,eL.jsx)("div",{className:"font-medium text-sm",children:e.filename}),(0,eL.jsxs)("div",{className:"text-xs text-gray-500",children:[e.document_type," • ",(e.content?.length/1e3||0).toFixed(1),"k tegn"]})]},e.id))})]}),l&&(0,eL.jsx)("div",{className:"lg:col-span-2",children:(0,eL.jsx)(Object(function(){var e=Error("Cannot find module '@/components/viewer/DocumentViewer'");throw e.code="MODULE_NOT_FOUND",e}()),{document:l,onClose:()=>d(null)})})]}),"chat"===s&&(0,eL.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-200px)]",children:[(0,eL.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,eL.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,eL.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Hurtig Upload"}),(0,eL.jsx)(Object(function(){var e=Error("Cannot find module '@/components/upload/PDFUpload'");throw e.code="MODULE_NOT_FOUND",e}()),{onDocumentUpload:p})]}),a.length>0&&(0,eL.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,eL.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Seneste Dokumenter"}),(0,eL.jsx)("div",{className:"space-y-2",children:a.slice(0,3).map(e=>(0,eL.jsxs)("button",{onClick:()=>d(e),className:"w-full text-left p-2 rounded border hover:bg-gray-50 text-sm",children:[(0,eL.jsx)("div",{className:"font-medium truncate",children:e.filename}),(0,eL.jsx)("div",{className:"text-xs text-gray-500",children:e.document_type})]},e.id))})]})]}),(0,eL.jsx)("div",{className:"lg:col-span-3 flex flex-col",children:(0,eL.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border flex-1 flex flex-col",children:[(0,eL.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,eL.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"AI Forsikringsr\xe5dgiver"}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Still sp\xf8rgsm\xe5l om forsikring eller upload dokumenter til analyse"})]}),(0,eL.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,eL.jsx)(eH,{messages:o})}),(0,eL.jsx)("div",{className:"border-t border-gray-200",children:(0,eL.jsx)(eU,{onSendMessage:f})})]})})]})]})}),(0,eL.jsx)(ez,{isOpen:r,onClose:()=>n(!1),onComplete:e=>{localStorage.setItem("consent-completed","true"),localStorage.setItem("user-consents",JSON.stringify(e)),n(!1)}})]}):(0,eL.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,eL.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,eL.jsxs)("div",{className:"text-center",children:[(0,eL.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:"AI Forsikringsguiden"}),(0,eL.jsx)("p",{className:"text-xl text-gray-600 mb-12 max-w-3xl mx-auto",children:"Intelligent forsikringsr\xe5dgivning p\xe5 dansk. F\xe5 svar p\xe5 dine sp\xf8rgsm\xe5l, analyser dokumenter, og optimer din forsikringsd\xe6kning med hj\xe6lp fra AI."}),(0,eL.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[(0,eL.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,eL.jsx)("div",{className:"text-3xl mb-4",children:"\uD83E\uDD16"}),(0,eL.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"AI Chat"}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm",children:"Still sp\xf8rgsm\xe5l og f\xe5 ekspert svar baseret p\xe5 dansk forsikringslovgivning"})]}),(0,eL.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,eL.jsx)("div",{className:"text-3xl mb-4",children:"\uD83D\uDCC4"}),(0,eL.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Dokumentanalyse"}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm",children:"Upload forsikringspapirer og f\xe5 automatisk analyse og forklaringer"})]}),(0,eL.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,eL.jsx)("div",{className:"text-3xl mb-4",children:"\uD83D\uDCA1"}),(0,eL.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Proaktive Indsigter"}),(0,eL.jsx)("p",{className:"text-gray-600 text-sm",children:"F\xe5 personaliserede anbefalinger til optimering af din forsikring"})]})]}),(0,eL.jsxs)("div",{className:"space-y-4",children:[(0,eL.jsx)("a",{href:"/auth/signup",className:"btn-primary inline-block px-8 py-3 text-lg",children:"Kom i gang gratis"}),(0,eL.jsx)("div",{children:(0,eL.jsx)("a",{href:"/auth/login",className:"text-insurance-blue hover:underline",children:"Har du allerede en konto? Log ind"})})]})]})})})}!function(){var e=Error("Cannot find module '@/components/upload/PDFUpload'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/viewer/DocumentViewer'");throw e.code="MODULE_NOT_FOUND",e}()},2579:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let r=new WeakMap,n=new WeakMap;function s(e){let t=r.get(e);return console.assert(null!=t,"'this' is expected an Event object, but got",e),t}function i(e){if(null!=e.passiveListener){"undefined"!=typeof console&&"function"==typeof console.error&&console.error("Unable to preventDefault inside passive event listener invocation.",e.passiveListener);return}e.event.cancelable&&(e.canceled=!0,"function"==typeof e.event.preventDefault&&e.event.preventDefault())}function o(e,t){r.set(this,{eventTarget:e,event:t,eventPhase:2,currentTarget:e,canceled:!1,stopped:!1,immediateStopped:!1,passiveListener:null,timeStamp:t.timeStamp||Date.now()}),Object.defineProperty(this,"isTrusted",{value:!1,enumerable:!0});let n=Object.keys(t);for(let e=0;e<n.length;++e){let t=n[e];t in this||Object.defineProperty(this,t,a(t))}}function a(e){return{get(){return s(this).event[e]},set(t){s(this).event[e]=t},configurable:!0,enumerable:!0}}function l(e,t){s(e).passiveListener=t}o.prototype={get type(){return s(this).event.type},get target(){return s(this).eventTarget},get currentTarget(){return s(this).currentTarget},composedPath(){let e=s(this).currentTarget;return null==e?[]:[e]},get NONE(){return 0},get CAPTURING_PHASE(){return 1},get AT_TARGET(){return 2},get BUBBLING_PHASE(){return 3},get eventPhase(){return s(this).eventPhase},stopPropagation(){let e=s(this);e.stopped=!0,"function"==typeof e.event.stopPropagation&&e.event.stopPropagation()},stopImmediatePropagation(){let e=s(this);e.stopped=!0,e.immediateStopped=!0,"function"==typeof e.event.stopImmediatePropagation&&e.event.stopImmediatePropagation()},get bubbles(){return!!s(this).event.bubbles},get cancelable(){return!!s(this).event.cancelable},preventDefault(){i(s(this))},get defaultPrevented(){return s(this).canceled},get composed(){return!!s(this).event.composed},get timeStamp(){return s(this).timeStamp},get srcElement(){return s(this).eventTarget},get cancelBubble(){return s(this).stopped},set cancelBubble(value){if(!value)return;let e=s(this);e.stopped=!0,"boolean"==typeof e.event.cancelBubble&&(e.event.cancelBubble=!0)},get returnValue(){return!s(this).canceled},set returnValue(value){value||i(s(this))},initEvent(){}},Object.defineProperty(o.prototype,"constructor",{value:o,configurable:!0,writable:!0}),"undefined"!=typeof window&&void 0!==window.Event&&(Object.setPrototypeOf(o.prototype,window.Event.prototype),n.set(window.Event.prototype,o));let c=new WeakMap;function u(e){return null!==e&&"object"==typeof e}function d(e){let t=c.get(e);if(null==t)throw TypeError("'this' is expected an EventTarget object, but got another value.");return t}function h(e,t){Object.defineProperty(e,`on${t}`,{get(){let e=d(this).get(t);for(;null!=e;){if(3===e.listenerType)return e.listener;e=e.next}return null},set(e){"function"==typeof e||u(e)||(e=null);let r=d(this),n=null,s=r.get(t);for(;null!=s;)3===s.listenerType?null!==n?n.next=s.next:null!==s.next?r.set(t,s.next):r.delete(t):n=s,s=s.next;if(null!==e){let s={listener:e,listenerType:3,passive:!1,once:!1,next:null};null===n?r.set(t,s):n.next=s}},configurable:!0,enumerable:!0})}function f(e){function t(){p.call(this)}t.prototype=Object.create(p.prototype,{constructor:{value:t,configurable:!0,writable:!0}});for(let r=0;r<e.length;++r)h(t.prototype,e[r]);return t}function p(){if(this instanceof p)return void c.set(this,new Map);if(1==arguments.length&&Array.isArray(arguments[0]))return f(arguments[0]);if(arguments.length>0){let e=Array(arguments.length);for(let t=0;t<arguments.length;++t)e[t]=arguments[t];return f(e)}throw TypeError("Cannot call a class as a function")}p.prototype={addEventListener(e,t,r){if(null==t)return;if("function"!=typeof t&&!u(t))throw TypeError("'listener' should be a function or an object.");let n=d(this),s=u(r),i=(s?r.capture:r)?1:2,o={listener:t,listenerType:i,passive:s&&!!r.passive,once:s&&!!r.once,next:null},a=n.get(e);if(void 0===a)return void n.set(e,o);let l=null;for(;null!=a;){if(a.listener===t&&a.listenerType===i)return;l=a,a=a.next}l.next=o},removeEventListener(e,t,r){if(null==t)return;let n=d(this),s=(u(r)?r.capture:r)?1:2,i=null,o=n.get(e);for(;null!=o;){if(o.listener===t&&o.listenerType===s)return void(null!==i?i.next=o.next:null!==o.next?n.set(e,o.next):n.delete(e));i=o,o=o.next}},dispatchEvent(e){if(null==e||"string"!=typeof e.type)throw TypeError('"event.type" should be a string.');let t=d(this),r=e.type,i=t.get(r);if(null==i)return!0;let c=new(function e(t){if(null==t||t===Object.prototype)return o;let r=n.get(t);return null==r&&(r=function(e,t){let r=Object.keys(t);if(0===r.length)return e;function n(t,r){e.call(this,t,r)}n.prototype=Object.create(e.prototype,{constructor:{value:n,configurable:!0,writable:!0}});for(let i=0;i<r.length;++i){let o=r[i];if(!(o in e.prototype)){let e="function"==typeof Object.getOwnPropertyDescriptor(t,o).value;Object.defineProperty(n.prototype,o,e?function(e){return{value(){let t=s(this).event;return t[e].apply(t,arguments)},configurable:!0,enumerable:!0}}(o):a(o))}}return n}(e(Object.getPrototypeOf(t)),t),n.set(t,r)),r}(Object.getPrototypeOf(e)))(this,e),u=null;for(;null!=i;){if(i.once?null!==u?u.next=i.next:null!==i.next?t.set(r,i.next):t.delete(r):u=i,l(c,i.passive?i.listener:null),"function"==typeof i.listener)try{i.listener.call(this,c)}catch(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e)}else 3!==i.listenerType&&"function"==typeof i.listener.handleEvent&&i.listener.handleEvent(c);if(s(c).immediateStopped)break;i=i.next}return l(c,null),s(c).eventPhase=0,s(c).currentTarget=null,!c.defaultPrevented}},Object.defineProperty(p.prototype,"constructor",{value:p,configurable:!0,writable:!0}),"undefined"!=typeof window&&void 0!==window.EventTarget&&Object.setPrototypeOf(p.prototype,window.EventTarget.prototype),t.defineEventAttribute=h,t.EventTarget=p,t.default=p,e.exports=p,e.exports.EventTarget=e.exports.default=p,e.exports.defineEventAttribute=h},2698:e=>{"use strict";e.exports={CURRENT_ID:Symbol("agentkeepalive#currentId"),CREATE_ID:Symbol("agentkeepalive#createId"),INIT_SOCKET:Symbol("agentkeepalive#initSocket"),CREATE_HTTPS_CONNECTION:Symbol("agentkeepalive#createHttpsConnection"),SOCKET_CREATED_TIME:Symbol("agentkeepalive#socketCreatedTime"),SOCKET_NAME:Symbol("agentkeepalive#socketName"),SOCKET_REQUEST_COUNT:Symbol("agentkeepalive#socketRequestCount"),SOCKET_REQUEST_FINISHED_COUNT:Symbol("agentkeepalive#socketRequestFinishedCount")}},3024:e=>{"use strict";e.exports=require("node:fs")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3245:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});let n=e=>"function"==typeof e},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3332:(e,t,r)=>{"use strict";var n=r(3210),s="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,o=n.useEffect,a=n.useLayoutEffect,l=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!s(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),s=n[0].inst,u=n[1];return a(function(){s.value=r,s.getSnapshot=t,c(s)&&u({inst:s})},[e,r,t]),o(function(){return c(s)&&u({inst:s}),e(function(){c(s)&&u({inst:s})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:u},3566:e=>{"use strict";e.exports=require("worker_threads")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4759:(e,t,r)=>{"use strict";let n=r(8990);e.exports=n,e.exports.HttpAgent=n,e.exports.HttpsAgent=r(9532),e.exports.constants=r(2698)},5191:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n,s,i=r(8767),o=function(e,t,r,n,s){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r},a=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)};class l extends i.Y{constructor(e,t,r={}){if(super(e,r),n.set(this,void 0),s.set(this,0),arguments.length<2)throw TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);o(this,n,String(t),"f");let i=void 0===r.lastModified?Date.now():Number(r.lastModified);Number.isNaN(i)||o(this,s,i,"f")}static[(n=new WeakMap,s=new WeakMap,Symbol.hasInstance)](e){return e instanceof i.Y&&"File"===e[Symbol.toStringTag]&&"string"==typeof e.name}get name(){return a(this,n,"f")}get lastModified(){return a(this,s,"f")}get webkitRelativePath(){return""}get[Symbol.toStringTag](){return"File"}}},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},7075:e=>{"use strict";e.exports=require("node:stream")},7379:(e,t,r)=>{"use strict";e.exports=r(3332)},7802:e=>{function t(e,t,r,n){return Math.round(e/r)+" "+n+(t>=1.5*r?"s":"")}e.exports=function(e,r){r=r||{};var n,s,i,o,a=typeof e;if("string"===a&&e.length>0){var l=e;if(!((l=String(l)).length>100)){var c=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(l);if(c){var u=parseFloat(c[1]);switch((c[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*u;case"weeks":case"week":case"w":return 6048e5*u;case"days":case"day":case"d":return 864e5*u;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*u;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*u;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*u;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return u;default:break}}}return}if("number"===a&&isFinite(e)){return r.long?(s=Math.abs(n=e))>=864e5?t(n,s,864e5,"day"):s>=36e5?t(n,s,36e5,"hour"):s>=6e4?t(n,s,6e4,"minute"):s>=1e3?t(n,s,1e3,"second"):n+" ms":(o=Math.abs(i=e))>=864e5?Math.round(i/864e5)+"d":o>=36e5?Math.round(i/36e5)+"h":o>=6e4?Math.round(i/6e4)+"m":o>=1e3?Math.round(i/1e3)+"s":i+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},7830:e=>{"use strict";e.exports=require("node:stream/web")},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},8355:(e,t,r)=>{"use strict";r.d(t,{f:()=>s});var n=r(5191);let s=e=>e instanceof n.Z},8767:(e,t,r)=>{"use strict";r.d(t,{Y:()=>rs});let n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:e=>`Symbol(${e})`;function s(){}function i(e){return"object"==typeof e&&null!==e||"function"==typeof e}function o(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}let a=Promise,l=Promise.prototype.then,c=Promise.resolve.bind(a),u=Promise.reject.bind(a);function d(e){return new a(e)}function h(e,t,r){return l.call(e,t,r)}function f(e,t,r){h(h(e,t,r),void 0,s)}function p(e){h(e,void 0,s)}let m=e=>{if("function"==typeof queueMicrotask)m=queueMicrotask;else{let e=c(void 0);m=t=>h(e,t)}return m(e)};function g(e,t,r){if("function"!=typeof e)throw TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function y(e,t,r){try{var n;return n=g(e,t,r),c(n)}catch(e){return u(e)}}class b{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){let t=this._back,r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){let e=this._front,t=e,r=this._cursor,n=r+1,s=e._elements,i=s[r];return 16384===n&&(t=e._next,n=0),--this._size,this._cursor=n,e!==t&&(this._front=t),s[r]=void 0,i}forEach(e){let t=this._cursor,r=this._front,n=r._elements;for(;!(t===n.length&&void 0===r._next||t===n.length&&(n=(r=r._next)._elements,t=0,0===n.length));)e(n[t]),++t}peek(){let e=this._front,t=this._cursor;return e._elements[t]}}let _=n("[[AbortSteps]]"),v=n("[[ErrorSteps]]"),w=n("[[CancelSteps]]"),x=n("[[PullSteps]]"),S=n("[[ReleaseSteps]]");function k(e,t){var r,n;e._ownerReadableStream=t,t._reader=e,"readable"===t._state?C(e):"closed"===t._state?(C(e),R(e)):(r=e,n=t._storedError,C(r),P(r,n))}function T(e,t){return tj(e._ownerReadableStream,t)}function j(e){var t,r;let n=e._ownerReadableStream;"readable"===n._state?P(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):(t=e,r=TypeError("Reader was released and can no longer be used to monitor the stream's closedness"),C(t),P(t,r)),n._readableStreamController[S](),n._reader=void 0,e._ownerReadableStream=void 0}function E(e){return TypeError("Cannot "+e+" a stream using a released reader")}function C(e){e._closedPromise=d((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}function P(e,t){void 0!==e._closedPromise_reject&&(p(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function R(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let A=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},O=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function N(e,t){if(void 0!==e&&"object"!=typeof e&&"function"!=typeof e)throw TypeError(`${t} is not an object.`)}function I(e,t){if("function"!=typeof e)throw TypeError(`${t} is not a function.`)}function $(e,t){if(("object"!=typeof e||null===e)&&"function"!=typeof e)throw TypeError(`${t} is not an object.`)}function q(e,t,r){if(void 0===e)throw TypeError(`Parameter ${t} is required in '${r}'.`)}function D(e,t,r){if(void 0===e)throw TypeError(`${t} is required in '${r}'.`)}function L(e){return Number(e)}function B(e,t){var r,n;let s=Number.MAX_SAFE_INTEGER,i=Number(e);if(!A(i=0===(r=i)?0:r))throw TypeError(`${t} is not a finite number`);if((i=0===(n=O(i))?0:n)<0||i>s)throw TypeError(`${t} is outside the accepted range of 0 to ${s}, inclusive`);return A(i)&&0!==i?i:0}function M(e){if(!i(e)||"function"!=typeof e.getReader)return!1;try{return"boolean"==typeof e.locked}catch(e){return!1}}function F(e){if(!i(e)||"function"!=typeof e.getWriter)return!1;try{return"boolean"==typeof e.locked}catch(e){return!1}}function W(e,t){if(!tk(e))throw TypeError(`${t} is not a ReadableStream.`)}function z(e,t){e._reader._readRequests.push(t)}function U(e,t,r){let n=e._reader._readRequests.shift();r?n._closeSteps():n._chunkSteps(t)}function H(e){return e._reader._readRequests.length}function V(e){let t=e._reader;return void 0!==t&&!!K(t)}class J{constructor(e){if(q(e,1,"ReadableStreamDefaultReader"),W(e,"First parameter"),tT(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");k(this,e),this._readRequests=new b}get closed(){return K(this)?this._closedPromise:u(G("closed"))}cancel(e){return K(this)?void 0===this._ownerReadableStream?u(E("cancel")):T(this,e):u(G("cancel"))}read(){let e,t;if(!K(this))return u(G("read"));if(void 0===this._ownerReadableStream)return u(E("read from"));let r=d((r,n)=>{e=r,t=n});return function(e,t){let r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[x](t)}(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!K(this))throw G("releaseLock");void 0!==this._ownerReadableStream&&(j(this),X(this,TypeError("Reader was released")))}}function K(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof J}function X(e,t){let r=e._readRequests;e._readRequests=new b,r.forEach(e=>{e._errorSteps(t)})}function G(e){return TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(J.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),o(J.prototype.cancel,"cancel"),o(J.prototype.read,"read"),o(J.prototype.releaseLock,"releaseLock"),"symbol"==typeof n.toStringTag&&Object.defineProperty(J.prototype,n.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});class Y{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){let e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?h(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){let t=()=>this._returnSteps(e);return this._ongoingPromise?h(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let e=this._reader;return void 0===e?u(E("iterate")):h(e.read(),e=>{var t;return this._ongoingPromise=void 0,e.done&&(this._isFinished=!0,null==(t=this._reader)||t.releaseLock(),this._reader=void 0),e},e=>{var t;throw this._ongoingPromise=void 0,this._isFinished=!0,null==(t=this._reader)||t.releaseLock(),this._reader=void 0,e})}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;let t=this._reader;if(void 0===t)return u(E("finish iterating"));if(this._reader=void 0,!this._preventCancel){let r=t.cancel(e);return t.releaseLock(),h(r,()=>({value:e,done:!0}),void 0)}return t.releaseLock(),c({value:e,done:!0})}}let Q={next(){return Z(this)?this._asyncIteratorImpl.next():u(ee("next"))},return(e){return Z(this)?this._asyncIteratorImpl.return(e):u(ee("return"))}};function Z(e){if(!i(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Y}catch(e){return!1}}function ee(e){return TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}"symbol"==typeof n.asyncIterator&&Object.defineProperty(Q,n.asyncIterator,{value(){return this},writable:!0,configurable:!0});let et=Number.isNaN||function(e){return e!=e};function er(e,t,r,n,s){new Uint8Array(e).set(new Uint8Array(r,n,s),t)}function en(e){return new Uint8Array(function(e,t,r){if(e.slice)return e.slice(t,r);let n=r-t,s=new ArrayBuffer(n);return er(s,0,e,t,n),s}(e.buffer,e.byteOffset,e.byteOffset+e.byteLength))}function es(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function ei(e,t,r){if("number"!=typeof r||et(r)||r<0||r===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function eo(e){e._queue=new b,e._queueTotalSize=0}class ea{constructor(){throw TypeError("Illegal constructor")}get view(){if(!eu(this))throw eP("view");return this._view}respond(e){if(!eu(this))throw eP("respond");if(q(e,1,"respond"),e=B(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");this._view.buffer,function(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw RangeError("bytesWritten out of range")}r.buffer=r.buffer,eS(e,t)}(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!eu(this))throw eP("respondWithNewView");if(q(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");e.buffer,function(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw RangeError("The region specified by view is larger than byobRequest");let n=t.byteLength;r.buffer=t.buffer,eS(e,n)}(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ea.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),o(ea.prototype.respond,"respond"),o(ea.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof n.toStringTag&&Object.defineProperty(ea.prototype,n.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class el{constructor(){throw TypeError("Illegal constructor")}get byobRequest(){if(!ec(this))throw eR("byobRequest");return function(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),n=Object.create(ea.prototype);n._associatedReadableByteStreamController=e,n._view=r,e._byobRequest=n}return e._byobRequest}(this)}get desiredSize(){if(!ec(this))throw eR("desiredSize");return eC(this)}close(){if(!ec(this))throw eR("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");let e=this._controlledReadableByteStream._state;if("readable"!==e)throw TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);!function(e){let t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){if(e._queueTotalSize>0)return e._closeRequested=!0;if(e._pendingPullIntos.length>0&&e._pendingPullIntos.peek().bytesFilled>0){let t=TypeError("Insufficient bytes to fill elements in the given buffer");throw ej(e,t),t}eT(e),tE(t)}}(this)}enqueue(e){if(!ec(this))throw eR("enqueue");if(q(e,1,"enqueue"),!ArrayBuffer.isView(e))throw TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");let t=this._controlledReadableByteStream._state;if("readable"!==t)throw TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);!function(e,t){let r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;let n=t.buffer,s=t.byteOffset,i=t.byteLength;if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();t.buffer,ew(e),t.buffer=t.buffer,"none"===t.readerType&&ey(e,t)}V(r)?(function(e){let t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;eE(e,t._readRequests.shift())}}(e),0===H(r))?em(e,n,s,i):(e._pendingPullIntos.length>0&&ek(e),U(r,new Uint8Array(n,s,i),!1)):eN(r)?(em(e,n,s,i),ex(e)):em(e,n,s,i),ed(e)}(this,e)}error(e){if(!ec(this))throw eR("error");ej(this,e)}[w](e){eh(this),eo(this);let t=this._cancelAlgorithm(e);return eT(this),t}[x](e){let t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void eE(this,e);let r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}let n={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(n)}z(t,e),ed(this)}[S](){if(this._pendingPullIntos.length>0){let e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new b,this._pendingPullIntos.push(e)}}}function ec(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof el}function eu(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof ea}function ed(e){if(function(e){let t=e._controlledReadableByteStream;return"readable"===t._state&&!e._closeRequested&&!!e._started&&!!(V(t)&&H(t)>0||eN(t)&&eO(t)>0||eC(e)>0)}(e)){if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0,f(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ed(e)),null),t=>(ej(e,t),null))}}function eh(e){ew(e),e._pendingPullIntos=new b}function ef(e,t){let r=!1;"closed"===e._state&&(r=!0);let n=ep(t);"default"===t.readerType?U(e,n,r):function(e,t,r){let n=e._reader._readIntoRequests.shift();r?n._closeSteps(t):n._chunkSteps(t)}(e,n,r)}function ep(e){let t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function em(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function eg(e,t,r,n){let s;try{s=t.slice(r,r+n)}catch(t){throw ej(e,t),t}em(e,s,0,n)}function ey(e,t){t.bytesFilled>0&&eg(e,t.buffer,t.byteOffset,t.bytesFilled),ek(e)}function eb(e,t){let r=t.elementSize,n=t.bytesFilled-t.bytesFilled%r,s=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),i=t.bytesFilled+s,o=i-i%r,a=s,l=!1;o>n&&(a=o-t.bytesFilled,l=!0);let c=e._queue;for(;a>0;){let r=c.peek(),n=Math.min(a,r.byteLength),s=t.byteOffset+t.bytesFilled;er(t.buffer,s,r.buffer,r.byteOffset,n),r.byteLength===n?c.shift():(r.byteOffset+=n,r.byteLength-=n),e._queueTotalSize-=n,e_(e,n,t),a-=n}return l}function e_(e,t,r){r.bytesFilled+=t}function ev(e){0===e._queueTotalSize&&e._closeRequested?(eT(e),tE(e._controlledReadableByteStream)):ed(e)}function ew(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function ex(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;let t=e._pendingPullIntos.peek();eb(e,t)&&(ek(e),ef(e._controlledReadableByteStream,t))}}function eS(e,t){let r=e._pendingPullIntos.peek();ew(e),"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&ek(e);let r=e._controlledReadableByteStream;if(eN(r))for(;eO(r)>0;)ef(r,ek(e))}(e,r):function(e,t,r){if(e_(0,t,r),"none"===r.readerType)return ey(e,r),ex(e);if(r.bytesFilled<r.elementSize)return;ek(e);let n=r.bytesFilled%r.elementSize;if(n>0){let t=r.byteOffset+r.bytesFilled;eg(e,r.buffer,t-n,n)}r.bytesFilled-=n,ef(e._controlledReadableByteStream,r),ex(e)}(e,t,r),ed(e)}function ek(e){return e._pendingPullIntos.shift()}function eT(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function ej(e,t){let r=e._controlledReadableByteStream;"readable"===r._state&&(eh(e),eo(e),eT(e),tC(r,t))}function eE(e,t){let r=e._queue.shift();e._queueTotalSize-=r.byteLength,ev(e);let n=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(n)}function eC(e){let t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function eP(e){return TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function eR(e){return TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function eA(e,t){e._reader._readIntoRequests.push(t)}function eO(e){return e._reader._readIntoRequests.length}function eN(e){let t=e._reader;return void 0!==t&&!!e$(t)}Object.defineProperties(el.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),o(el.prototype.close,"close"),o(el.prototype.enqueue,"enqueue"),o(el.prototype.error,"error"),"symbol"==typeof n.toStringTag&&Object.defineProperty(el.prototype,n.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class eI{constructor(e){if(q(e,1,"ReadableStreamBYOBReader"),W(e,"First parameter"),tT(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!ec(e._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");k(this,e),this._readIntoRequests=new b}get closed(){return e$(this)?this._closedPromise:u(eD("closed"))}cancel(e){return e$(this)?void 0===this._ownerReadableStream?u(E("cancel")):T(this,e):u(eD("cancel"))}read(e){let t,r;if(!e$(this))return u(eD("read"));if(!ArrayBuffer.isView(e))return u(TypeError("view must be an array buffer view"));if(0===e.byteLength)return u(TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return u(TypeError("view's buffer must have non-zero byteLength"));if(e.buffer,void 0===this._ownerReadableStream)return u(E("read from"));let n=d((e,n)=>{t=e,r=n});return function(e,t,r){let n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?r._errorSteps(n._storedError):function(e,t,r){let n=e._controlledReadableByteStream,s=1;t.constructor!==DataView&&(s=t.constructor.BYTES_PER_ELEMENT);let i=t.constructor,o=t.buffer,a={buffer:o,bufferByteLength:o.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:s,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(a),eA(n,r);if("closed"!==n._state){if(e._queueTotalSize>0){if(eb(e,a)){let t=ep(a);return ev(e),r._chunkSteps(t)}if(e._closeRequested){let t=TypeError("Insufficient bytes to fill elements in the given buffer");return ej(e,t),r._errorSteps(t)}}e._pendingPullIntos.push(a),eA(n,r),ed(e)}else{let e=new i(a.buffer,a.byteOffset,0);r._closeSteps(e)}}(n._readableStreamController,t,r)}(this,e,{_chunkSteps:e=>t({value:e,done:!1}),_closeSteps:e=>t({value:e,done:!0}),_errorSteps:e=>r(e)}),n}releaseLock(){if(!e$(this))throw eD("releaseLock");void 0!==this._ownerReadableStream&&(j(this),eq(this,TypeError("Reader was released")))}}function e$(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof eI}function eq(e,t){let r=e._readIntoRequests;e._readIntoRequests=new b,r.forEach(e=>{e._errorSteps(t)})}function eD(e){return TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function eL(e,t){let{highWaterMark:r}=e;if(void 0===r)return t;if(et(r)||r<0)throw RangeError("Invalid highWaterMark");return r}function eB(e){let{size:t}=e;return t||(()=>1)}function eM(e,t){var r;N(e,t);let n=null==e?void 0:e.highWaterMark,s=null==e?void 0:e.size;return{highWaterMark:void 0===n?void 0:L(n),size:void 0===s?void 0:(I(r=s,`${t} has member 'size' that`),e=>L(r(e)))}}Object.defineProperties(eI.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),o(eI.prototype.cancel,"cancel"),o(eI.prototype.read,"read"),o(eI.prototype.releaseLock,"releaseLock"),"symbol"==typeof n.toStringTag&&Object.defineProperty(eI.prototype,n.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});let eF="function"==typeof AbortController;class eW{constructor(e={},t={}){void 0===e?e=null:$(e,"First parameter");let r=eM(t,"Second parameter"),n=function(e,t){N(e,t);let r=null==e?void 0:e.abort,n=null==e?void 0:e.close,s=null==e?void 0:e.start,i=null==e?void 0:e.type,o=null==e?void 0:e.write;return{abort:void 0===r?void 0:(I(r,`${t} has member 'abort' that`),t=>y(r,e,[t])),close:void 0===n?void 0:(I(n,`${t} has member 'close' that`),()=>y(n,e,[])),start:void 0===s?void 0:(I(s,`${t} has member 'start' that`),t=>g(s,e,[t])),write:void 0===o?void 0:(I(o,`${t} has member 'write' that`),(t,r)=>y(o,e,[t,r])),type:i}}(e,"First parameter");if(this._state="writable",this._storedError=void 0,this._writer=void 0,this._writableStreamController=void 0,this._writeRequests=new b,this._inFlightWriteRequest=void 0,this._closeRequest=void 0,this._inFlightCloseRequest=void 0,this._pendingAbortRequest=void 0,this._backpressure=!1,void 0!==n.type)throw RangeError("Invalid type is specified");let s=eB(r);!function(e,t,r,n){let s,i,o,a,l=Object.create(e3.prototype);s=void 0!==t.start?()=>t.start(l):()=>{},i=void 0!==t.write?e=>t.write(e,l):()=>c(void 0),o=void 0!==t.close?()=>t.close():()=>c(void 0),a=void 0!==t.abort?e=>t.abort(e):()=>c(void 0),l._controlledWritableStream=e,e._writableStreamController=l,l._queue=void 0,l._queueTotalSize=void 0,eo(l),l._abortReason=void 0,l._abortController=function(){if(eF)return new AbortController}(),l._started=!1,l._strategySizeAlgorithm=n,l._strategyHWM=r,l._writeAlgorithm=i,l._closeAlgorithm=o,l._abortAlgorithm=a,eQ(e,0>=e8(l)),f(c(s()),()=>(l._started=!0,e5(l),null),t=>(l._started=!0,eJ(e,t),null))}(this,n,eL(r,1),s)}get locked(){if(!ez(this))throw te("locked");return eU(this)}abort(e){return ez(this)?eU(this)?u(TypeError("Cannot abort a stream that already has a writer")):eH(this,e):u(te("abort"))}close(){return ez(this)?eU(this)?u(TypeError("Cannot close a stream that already has a writer")):eG(this)?u(TypeError("Cannot close an already-closing stream")):eV(this):u(te("close"))}getWriter(){if(!ez(this))throw te("getWriter");return new eZ(this)}}function ez(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof eW}function eU(e){return void 0!==e._writer}function eH(e,t){var r;if("closed"===e._state||"errored"===e._state)return c(void 0);e._writableStreamController._abortReason=t,null==(r=e._writableStreamController._abortController)||r.abort(t);let n=e._state;if("closed"===n||"errored"===n)return c(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let s=!1;"erroring"===n&&(s=!0,t=void 0);let i=d((r,n)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:s}});return e._pendingAbortRequest._promise=i,s||eK(e,t),i}function eV(e){var t;let r=e._state;if("closed"===r||"errored"===r)return u(TypeError(`The stream (in ${r} state) is not in the writable state and cannot be closed`));let n=d((t,r)=>{e._closeRequest={_resolve:t,_reject:r}}),s=e._writer;return void 0!==s&&e._backpressure&&"writable"===r&&tu(s),ei(t=e._writableStreamController,e2,0),e5(t),n}function eJ(e,t){"writable"!==e._state?eX(e):eK(e,t)}function eK(e,t){let r=e._writableStreamController;e._state="erroring",e._storedError=t;let n=e._writer;void 0!==n&&e1(n,t),void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest&&r._started&&eX(e)}function eX(e){e._state="errored",e._writableStreamController[v]();let t=e._storedError;if(e._writeRequests.forEach(e=>{e._reject(t)}),e._writeRequests=new b,void 0===e._pendingAbortRequest)return void eY(e);let r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void eY(e);f(e._writableStreamController[_](r._reason),()=>(r._resolve(),eY(e),null),t=>(r._reject(t),eY(e),null))}function eG(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function eY(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;void 0!==t&&ti(t,e._storedError)}function eQ(e,t){let r=e._writer;void 0!==r&&t!==e._backpressure&&(t?ta(r):tu(r)),e._backpressure=t}Object.defineProperties(eW.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),o(eW.prototype.abort,"abort"),o(eW.prototype.close,"close"),o(eW.prototype.getWriter,"getWriter"),"symbol"==typeof n.toStringTag&&Object.defineProperty(eW.prototype,n.toStringTag,{value:"WritableStream",configurable:!0});class eZ{constructor(e){if(q(e,1,"WritableStreamDefaultWriter"),function(e,t){if(!ez(e))throw TypeError(`${t} is not a WritableStream.`)}(e,"First parameter"),eU(e))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;let t=e._state;if("writable"===t)!eG(e)&&e._backpressure?ta(this):function(e){ta(e),tu(e)}(this),ts(this);else if("erroring"===t)tl(this,e._storedError),ts(this);else if("closed"===t)(function(e){ta(e),tu(e)})(this),ts(this),to(this);else{let t=e._storedError;tl(this,t),function(e,t){ts(e),ti(e,t)}(this,t)}}get closed(){return e0(this)?this._closedPromise:u(tr("closed"))}get desiredSize(){if(!e0(this))throw tr("desiredSize");if(void 0===this._ownerWritableStream)throw tn("desiredSize");let e=this._ownerWritableStream,t=e._state;return"errored"===t||"erroring"===t?null:"closed"===t?0:e8(e._writableStreamController)}get ready(){return e0(this)?this._readyPromise:u(tr("ready"))}abort(e){return e0(this)?void 0===this._ownerWritableStream?u(tn("abort")):eH(this._ownerWritableStream,e):u(tr("abort"))}close(){if(!e0(this))return u(tr("close"));let e=this._ownerWritableStream;return void 0===e?u(tn("close")):eG(e)?u(TypeError("Cannot close an already-closing stream")):eV(this._ownerWritableStream)}releaseLock(){if(!e0(this))throw tr("releaseLock");void 0!==this._ownerWritableStream&&function(e){var t,r;let n=e._ownerWritableStream,s=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");e1(e,s),"pending"===e._closedPromiseState?ti(e,s):(t=e,r=s,ts(t),ti(t,r)),n._writer=void 0,e._ownerWritableStream=void 0}(this)}write(e){return e0(this)?void 0===this._ownerWritableStream?u(tn("write to")):function(e,t){let r=e._ownerWritableStream,n=r._writableStreamController,s=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return e9(e,t),1}}(n,t);if(r!==e._ownerWritableStream)return u(tn("write to"));let i=r._state;if("errored"===i)return u(r._storedError);if(eG(r)||"closed"===i)return u(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return u(r._storedError);let o=d((e,t)=>{r._writeRequests.push({_resolve:e,_reject:t})});return function(e,t,r){try{ei(e,t,r)}catch(t){return void e9(e,t)}let n=e._controlledWritableStream;eG(n)||"writable"!==n._state||eQ(n,0>=e8(e)),e5(e)}(n,t,s),o}(this,e):u(tr("write"))}}function e0(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof eZ}function e1(e,t){"pending"===e._readyPromiseState?tc(e,t):tl(e,t)}Object.defineProperties(eZ.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),o(eZ.prototype.abort,"abort"),o(eZ.prototype.close,"close"),o(eZ.prototype.releaseLock,"releaseLock"),o(eZ.prototype.write,"write"),"symbol"==typeof n.toStringTag&&Object.defineProperty(eZ.prototype,n.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});let e2={};class e3{constructor(){throw TypeError("Illegal constructor")}get abortReason(){if(!e4(this))throw tt("abortReason");return this._abortReason}get signal(){if(!e4(this))throw tt("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e){if(!e4(this))throw tt("error");"writable"===this._controlledWritableStream._state&&e7(this,e)}[_](e){let t=this._abortAlgorithm(e);return e6(this),t}[v](){eo(this)}}function e4(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof e3}function e6(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function e8(e){return e._strategyHWM-e._queueTotalSize}function e5(e){let t=e._controlledWritableStream;if(!e._started||void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void eX(t);if(0===e._queue.length)return;let r=e._queue.peek().value;r===e2?function(e){let t=e._controlledWritableStream;t._inFlightCloseRequest=t._closeRequest,t._closeRequest=void 0,es(e);let r=e._closeAlgorithm();e6(e),f(r,()=>((function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let t=e._writer;void 0!==t&&to(t)})(t),null),e=>(t._inFlightCloseRequest._reject(e),t._inFlightCloseRequest=void 0,void 0!==t._pendingAbortRequest&&(t._pendingAbortRequest._reject(e),t._pendingAbortRequest=void 0),eJ(t,e),null))}(e):function(e,t){let r=e._controlledWritableStream;r._inFlightWriteRequest=r._writeRequests.shift(),f(e._writeAlgorithm(t),()=>{r._inFlightWriteRequest._resolve(void 0),r._inFlightWriteRequest=void 0;let t=r._state;return es(e),eG(r)||"writable"!==t||eQ(r,0>=e8(e)),e5(e),null},t=>("writable"===r._state&&e6(e),r._inFlightWriteRequest._reject(t),r._inFlightWriteRequest=void 0,eJ(r,t),null))}(e,r)}function e9(e,t){"writable"===e._controlledWritableStream._state&&e7(e,t)}function e7(e,t){let r=e._controlledWritableStream;e6(e),eK(r,t)}function te(e){return TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function tt(e){return TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function tr(e){return TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function tn(e){return TypeError("Cannot "+e+" a stream using a released writer")}function ts(e){e._closedPromise=d((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}function ti(e,t){void 0!==e._closedPromise_reject&&(p(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function to(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function ta(e){e._readyPromise=d((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}function tl(e,t){ta(e),tc(e,t)}function tc(e,t){void 0!==e._readyPromise_reject&&(p(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function tu(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(e3.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof n.toStringTag&&Object.defineProperty(e3.prototype,n.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});let td="undefined"!=typeof DOMException?DOMException:void 0,th=!function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(td)?function(){let e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}():td;function tf(e,t,r,n,s,i){let o=e.getReader(),a=t.getWriter();tk(e)&&(e._disturbed=!0);let l,g,y,b=!1,_=!1,v="readable",w="writable",x=!1,S=!1,k=d(e=>{y=e}),T=Promise.resolve(void 0);return d((j,E)=>{let C;function P(){if(b)return;let e=d((e,t)=>{!function r(n){n?e():h(b?c(!0):h(a.ready,()=>h(o.read(),e=>!!e.done||(p(T=a.write(e.value)),!1))),r,t)}(!1)});p(e)}function R(){return v="closed",r?I():N(()=>(ez(t)&&(x=eG(t),w=t._state),x||"closed"===w?c(void 0):"erroring"===w||"errored"===w?u(g):(x=!0,a.close())),!1,void 0),null}function A(e){return b||(v="errored",l=e,n?I(!0,e):N(()=>a.abort(e),!0,e)),null}function O(e){return _||(w="errored",g=e,s?I(!0,e):N(()=>o.cancel(e),!0,e)),null}if(void 0!==i&&(C=()=>{let e=void 0!==i.reason?i.reason:new th("Aborted","AbortError"),t=[];n||t.push(()=>"writable"===w?a.abort(e):c(void 0)),s||t.push(()=>"readable"===v?o.cancel(e):c(void 0)),N(()=>Promise.all(t.map(e=>e())),!0,e)},i.aborted?C():i.addEventListener("abort",C)),tk(e)&&(v=e._state,l=e._storedError),ez(t)&&(w=t._state,g=t._storedError,x=eG(t)),tk(e)&&ez(t)&&(S=!0,y()),"errored"===v)A(l);else if("erroring"===w||"errored"===w)O(g);else if("closed"===v)R();else if(x||"closed"===w){let e=TypeError("the destination writable stream closed before all data could be piped to it");s?I(!0,e):N(()=>o.cancel(e),!0,e)}function N(e,t,r){function n(){let e;return"writable"!==w||x?s():f(c(function t(){if(e!==T)return e=T,h(T,t,t)}()),s),null}function s(){return e?f(e(),()=>$(t,r),e=>$(!0,e)):$(t,r),null}b||(b=!0,S?n():f(k,n))}function I(e,t){N(void 0,e,t)}function $(e,t){return _=!0,a.releaseLock(),o.releaseLock(),void 0!==i&&i.removeEventListener("abort",C),e?E(t):j(void 0),null}b||(f(o.closed,R,A),f(a.closed,function(){return _||(w="closed"),null},O)),S?P():m(()=>{S=!0,y(),P()})})}class tp{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!tm(this))throw tw("desiredSize");return t_(this)}close(){if(!tm(this))throw tw("close");if(!tv(this))throw TypeError("The stream is not in a state that permits close");!function(e){if(!tv(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(ty(e),tE(t))}(this)}enqueue(e){if(!tm(this))throw tw("enqueue");if(!tv(this))throw TypeError("The stream is not in a state that permits enqueue");if(!tv(this))return;let t=this._controlledReadableStream;if(tT(t)&&H(t)>0)U(t,e,!1);else{let t;try{t=this._strategySizeAlgorithm(e)}catch(e){throw tb(this,e),e}try{ei(this,e,t)}catch(e){throw tb(this,e),e}}tg(this)}error(e){if(!tm(this))throw tw("error");tb(this,e)}[w](e){eo(this);let t=this._cancelAlgorithm(e);return ty(this),t}[x](e){let t=this._controlledReadableStream;if(this._queue.length>0){let r=es(this);this._closeRequested&&0===this._queue.length?(ty(this),tE(t)):tg(this),e._chunkSteps(r)}else z(t,e),tg(this)}[S](){}}function tm(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof tp}function tg(e){if(function(e){let t=e._controlledReadableStream;return!!tv(e)&&!!e._started&&!!(tT(t)&&H(t)>0||t_(e)>0)}(e)){if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0,f(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,tg(e)),null),t=>(tb(e,t),null))}}function ty(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function tb(e,t){let r=e._controlledReadableStream;"readable"===r._state&&(eo(e),ty(e),tC(r,t))}function t_(e){let t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function tv(e){return!e._closeRequested&&"readable"===e._controlledReadableStream._state}function tw(e){return TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function tx(e,t){N(e,t);let r=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,s=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw TypeError(`${t} is not an AbortSignal.`)}(i,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!n,preventClose:!!s,signal:i}}Object.defineProperties(tp.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),o(tp.prototype.close,"close"),o(tp.prototype.enqueue,"enqueue"),o(tp.prototype.error,"error"),"symbol"==typeof n.toStringTag&&Object.defineProperty(tp.prototype,n.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class tS{constructor(e={},t={}){void 0===e?e=null:$(e,"First parameter");let r=eM(t,"Second parameter"),n=function(e,t){N(e,t);let r=null==e?void 0:e.autoAllocateChunkSize,n=null==e?void 0:e.cancel,s=null==e?void 0:e.pull,i=null==e?void 0:e.start,o=null==e?void 0:e.type;return{autoAllocateChunkSize:void 0===r?void 0:B(r,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:(I(n,`${t} has member 'cancel' that`),t=>y(n,e,[t])),pull:void 0===s?void 0:(I(s,`${t} has member 'pull' that`),t=>y(s,e,[t])),start:void 0===i?void 0:(I(i,`${t} has member 'start' that`),t=>g(i,e,[t])),type:void 0===o?void 0:function(e,t){if("bytes"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}(o,`${t} has member 'type' that`)}}(e,"First parameter");if(this._state="readable",this._reader=void 0,this._storedError=void 0,this._disturbed=!1,"bytes"===n.type){if(void 0!==r.size)throw RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){let n,s,i,o=Object.create(el.prototype);n=void 0!==t.start?()=>t.start(o):()=>{},s=void 0!==t.pull?()=>t.pull(o):()=>c(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>c(void 0);let a=t.autoAllocateChunkSize;if(0===a)throw TypeError("autoAllocateChunkSize must be greater than 0");o._controlledReadableByteStream=e,o._pullAgain=!1,o._pulling=!1,o._byobRequest=null,o._queue=o._queueTotalSize=void 0,eo(o),o._closeRequested=!1,o._started=!1,o._strategyHWM=r,o._pullAlgorithm=s,o._cancelAlgorithm=i,o._autoAllocateChunkSize=a,o._pendingPullIntos=new b,e._readableStreamController=o,f(c(n()),()=>(o._started=!0,ed(o),null),e=>(ej(o,e),null))}(this,n,eL(r,0))}else{let e=eB(r);!function(e,t,r,n){let s,i,o,a=Object.create(tp.prototype);s=void 0!==t.start?()=>t.start(a):()=>{},i=void 0!==t.pull?()=>t.pull(a):()=>c(void 0),o=void 0!==t.cancel?e=>t.cancel(e):()=>c(void 0),a._controlledReadableStream=e,a._queue=void 0,a._queueTotalSize=void 0,eo(a),a._started=!1,a._closeRequested=!1,a._pullAgain=!1,a._pulling=!1,a._strategySizeAlgorithm=n,a._strategyHWM=r,a._pullAlgorithm=i,a._cancelAlgorithm=o,e._readableStreamController=a,f(c(s()),()=>(a._started=!0,tg(a),null),e=>(tb(a,e),null))}(this,n,eL(r,1),e)}}get locked(){if(!tk(this))throw tP("locked");return tT(this)}cancel(e){return tk(this)?tT(this)?u(TypeError("Cannot cancel a stream that already has a reader")):tj(this,e):u(tP("cancel"))}getReader(e){if(!tk(this))throw tP("getReader");return void 0===function(e,t){N(e,t);let r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:function(e,t){if("byob"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?new J(this):new eI(this)}pipeThrough(e,t={}){if(!M(this))throw tP("pipeThrough");q(e,1,"pipeThrough");let r=function(e,t){N(e,t);let r=null==e?void 0:e.readable;D(r,"readable","ReadableWritablePair"),function(e,t){if(!M(e))throw TypeError(`${t} is not a ReadableStream.`)}(r,`${t} has member 'readable' that`);let n=null==e?void 0:e.writable;return D(n,"writable","ReadableWritablePair"),function(e,t){if(!F(e))throw TypeError(`${t} is not a WritableStream.`)}(n,`${t} has member 'writable' that`),{readable:r,writable:n}}(e,"First parameter"),n=tx(t,"Second parameter");if(this.locked)throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(r.writable.locked)throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return p(tf(this,r.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),r.readable}pipeTo(e,t={}){let r;if(!M(this))return u(tP("pipeTo"));if(void 0===e)return u("Parameter 1 is required in 'pipeTo'.");if(!F(e))return u(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{r=tx(t,"Second parameter")}catch(e){return u(e)}return this.locked?u(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):e.locked?u(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):tf(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!M(this))throw tP("tee");if(this.locked)throw TypeError("Cannot tee a stream that already has a reader");return!function(e){try{return e.getReader({mode:"byob"}).releaseLock(),!0}catch(e){return!1}}(this)?function(e,t){let r=e.getReader(),n,s,i,o,a,l=!1,u=!1,h=!1,p=!1,m=d(e=>{a=e});function g(){return l?u=!0:(l=!0,f(r.read(),e=>{if(u=!1,e.done)return h||i.close(),p||o.close(),h&&p||a(void 0),null;let t=e.value;return h||i.enqueue(t),p||o.enqueue(t),l=!1,u&&g(),null},()=>(l=!1,null))),c(void 0)}let y=new tS({start(e){i=e},pull:g,cancel:function(e){if(h=!0,n=e,p){let e=[n,s],t=r.cancel(e);a(t)}return m}}),b=new tS({start(e){o=e},pull:g,cancel:function(e){if(p=!0,s=e,h){let e=[n,s],t=r.cancel(e);a(t)}return m}});return f(r.closed,void 0,e=>(i.error(e),o.error(e),h&&p||a(void 0),null)),[y,b]}(this):function(e){let t,r,n,s,i,o=e.getReader(),a=!1,l=!1,u=!1,h=!1,p=!1,m=!1,g=d(e=>{i=e});function y(e){f(e.closed,void 0,t=>(e!==o||(n.error(t),s.error(t),p&&m||i(void 0)),null))}function b(){a&&(o.releaseLock(),y(o=e.getReader()),a=!1),f(o.read(),e=>{var t,r;if(u=!1,h=!1,e.done)return p||n.close(),m||s.close(),null==(t=n.byobRequest)||t.respond(0),null==(r=s.byobRequest)||r.respond(0),p&&m||i(void 0),null;let a=e.value,c=a;if(!p&&!m)try{c=en(a)}catch(e){return n.error(e),s.error(e),i(o.cancel(e)),null}return p||n.enqueue(a),m||s.enqueue(c),l=!1,u?v():h&&w(),null},()=>(l=!1,null))}function _(t,r){a||(o.releaseLock(),y(o=e.getReader({mode:"byob"})),a=!0);let c=r?s:n,d=r?n:s;f(o.read(t),e=>{var t;u=!1,h=!1;let n=r?m:p,s=r?p:m;if(e.done){n||c.close(),s||d.close();let r=e.value;return void 0!==r&&(n||c.byobRequest.respondWithNewView(r),s||null==(t=d.byobRequest)||t.respond(0)),n&&s||i(void 0),null}let a=e.value;if(s)n||c.byobRequest.respondWithNewView(a);else{let e;try{e=en(a)}catch(e){return c.error(e),d.error(e),i(o.cancel(e)),null}n||c.byobRequest.respondWithNewView(a),d.enqueue(e)}return l=!1,u?v():h&&w(),null},()=>(l=!1,null))}function v(){if(l)return u=!0,c(void 0);l=!0;let e=n.byobRequest;return null===e?b():_(e.view,!1),c(void 0)}function w(){if(l)return h=!0,c(void 0);l=!0;let e=s.byobRequest;return null===e?b():_(e.view,!0),c(void 0)}let x=new tS({type:"bytes",start(e){n=e},pull:v,cancel:function(e){if(p=!0,t=e,m){let e=[t,r],n=o.cancel(e);i(n)}return g}}),S=new tS({type:"bytes",start(e){s=e},pull:w,cancel:function(e){if(m=!0,r=e,p){let e=[t,r],n=o.cancel(e);i(n)}return g}});return y(o),[x,S]}(this)}values(e){if(!M(this))throw tP("values");return function(e,t){let r=new Y(e.getReader(),t),n=Object.create(Q);return n._asyncIteratorImpl=r,n}(this,(N(e,"First parameter"),{preventCancel:!!(null==e?void 0:e.preventCancel)}).preventCancel)}}function tk(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof tS}function tT(e){return void 0!==e._reader}function tj(e,t){if(e._disturbed=!0,"closed"===e._state)return c(void 0);if("errored"===e._state)return u(e._storedError);tE(e);let r=e._reader;if(void 0!==r&&e$(r)){let e=r._readIntoRequests;r._readIntoRequests=new b,e.forEach(e=>{e._closeSteps(void 0)})}return h(e._readableStreamController[w](t),s,void 0)}function tE(e){e._state="closed";let t=e._reader;if(void 0!==t&&(R(t),K(t))){let e=t._readRequests;t._readRequests=new b,e.forEach(e=>{e._closeSteps()})}}function tC(e,t){e._state="errored",e._storedError=t;let r=e._reader;void 0!==r&&(P(r,t),K(r)?X(r,t):eq(r,t))}function tP(e){return TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function tR(e,t){N(e,t);let r=null==e?void 0:e.highWaterMark;return D(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:L(r)}}Object.defineProperties(tS.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),o(tS.prototype.cancel,"cancel"),o(tS.prototype.getReader,"getReader"),o(tS.prototype.pipeThrough,"pipeThrough"),o(tS.prototype.pipeTo,"pipeTo"),o(tS.prototype.tee,"tee"),o(tS.prototype.values,"values"),"symbol"==typeof n.toStringTag&&Object.defineProperty(tS.prototype,n.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof n.asyncIterator&&Object.defineProperty(tS.prototype,n.asyncIterator,{value:tS.prototype.values,writable:!0,configurable:!0});let tA=e=>e.byteLength;o(tA,"size");class tO{constructor(e){q(e,1,"ByteLengthQueuingStrategy"),e=tR(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!tI(this))throw tN("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!tI(this))throw tN("size");return tA}}function tN(e){return TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function tI(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof tO}Object.defineProperties(tO.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof n.toStringTag&&Object.defineProperty(tO.prototype,n.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});let t$=()=>1;o(t$,"size");class tq{constructor(e){q(e,1,"CountQueuingStrategy"),e=tR(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!tL(this))throw tD("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!tL(this))throw tD("size");return t$}}function tD(e){return TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function tL(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof tq}Object.defineProperties(tq.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof n.toStringTag&&Object.defineProperty(tq.prototype,n.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class tB{constructor(e={},t={},r={}){let n;void 0===e&&(e=null);let s=eM(t,"Second parameter"),i=eM(r,"Third parameter"),o=function(e,t){N(e,t);let r=null==e?void 0:e.flush,n=null==e?void 0:e.readableType,s=null==e?void 0:e.start,i=null==e?void 0:e.transform,o=null==e?void 0:e.writableType;return{flush:void 0===r?void 0:(I(r,`${t} has member 'flush' that`),t=>y(r,e,[t])),readableType:n,start:void 0===s?void 0:(I(s,`${t} has member 'start' that`),t=>g(s,e,[t])),transform:void 0===i?void 0:(I(i,`${t} has member 'transform' that`),(t,r)=>y(i,e,[t,r])),writableType:o}}(e,"First parameter");if(void 0!==o.readableType)throw RangeError("Invalid readableType specified");if(void 0!==o.writableType)throw RangeError("Invalid writableType specified");let a=eL(i,0),l=eB(i),f=eL(s,1),p=eB(s);!function(e,t,r,n,s,i){var o,a,l;function u(){return t}e._writableState="writable",e._writableStoredError=void 0,e._writableHasInFlightOperation=!1,e._writableStarted=!1,o=function(t){let r=e._transformStreamController;return e._backpressure?h(e._backpressureChangePromise,()=>{if("erroring"===(ez(e._writable)?e._writable._state:e._writableState))throw ez(e._writable)?e._writable._storedError:e._writableStoredError;return tK(r,t)},void 0):tK(r,t)},a=function(){var t=e;let r=t._transformStreamController,n=r._flushAlgorithm();return tV(r),h(n,()=>{if("errored"===t._readableState)throw t._readableStoredError;tY(t)&&tQ(t)},e=>{throw tF(t,e),t._readableStoredError})},e._writable=new eW({start(t){e._writableController=t;try{let r=t.signal;void 0!==r&&r.addEventListener("abort",()=>{"writable"===e._writableState&&(e._writableState="erroring",r.reason&&(e._writableStoredError=r.reason))})}catch(e){}return h(u(),()=>(e._writableStarted=!0,t4(e),null),t=>{throw e._writableStarted=!0,t1(e,t),t})},write:t=>(e._writableHasInFlightOperation=!0,h(o(t),()=>(e._writableHasInFlightOperation=!1,t4(e),null),t=>{throw e._writableHasInFlightOperation=!1,t1(e,t),t})),close:()=>(e._writableHasInFlightOperation=!0,h(a(),()=>(e._writableHasInFlightOperation=!1,"erroring"===e._writableState&&(e._writableStoredError=void 0),e._writableState="closed",null),t=>{throw e._writableHasInFlightOperation=!1,e._writableState,t1(e,t),t})),abort:t=>(e._writableState="errored",e._writableStoredError=t,function(t){return tF(e,t),c(void 0)}(t))},{highWaterMark:r,size:n}),e._readableState="readable",e._readableStoredError=void 0,e._readableCloseRequested=!1,e._readablePulling=!1,l=function(){return tz(e,!1),e._backpressureChangePromise},e._readable=new tS({start:t=>(e._readableController=t,u().catch(t=>{tZ(e,t)})),pull:()=>(e._readablePulling=!0,l().catch(t=>{tZ(e,t)})),cancel:t=>(e._readableState="closed",function(t){return tW(e,t),c(void 0)}(t))},{highWaterMark:s,size:i}),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,tz(e,!0),e._transformStreamController=void 0}(this,d(e=>{n=e}),f,p,a,l),function(e,t){let r,n,s=Object.create(tU.prototype);r=void 0!==t.transform?e=>t.transform(e,s):e=>{try{var t;return tJ(s,e),t=void 0,c(t)}catch(e){return u(e)}},n=void 0!==t.flush?()=>t.flush(s):()=>c(void 0),s._controlledTransformStream=e,e._transformStreamController=s,s._transformAlgorithm=r,s._flushAlgorithm=n}(this,o),void 0!==o.start?n(o.start(this._transformStreamController)):n(void 0)}get readable(){if(!tM(this))throw tG("readable");return this._readable}get writable(){if(!tM(this))throw tG("writable");return this._writable}}function tM(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof tB}function tF(e,t){tZ(e,t),tW(e,t)}function tW(e,t){tV(e._transformStreamController),e._writableController.error(t),"writable"===e._writableState&&t2(e,t),e._backpressure&&tz(e,!1)}function tz(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=d(t=>{e._backpressureChangePromise_resolve=t}),e._backpressure=t}Object.defineProperties(tB.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof n.toStringTag&&Object.defineProperty(tB.prototype,n.toStringTag,{value:"TransformStream",configurable:!0});class tU{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!tH(this))throw tX("desiredSize");return t0(this._controlledTransformStream)}enqueue(e){if(!tH(this))throw tX("enqueue");tJ(this,e)}error(e){if(!tH(this))throw tX("error");tF(this._controlledTransformStream,e)}terminate(){if(!tH(this))throw tX("terminate");let e=this._controlledTransformStream;tY(e)&&tQ(e),tW(e,TypeError("TransformStream terminated"))}}function tH(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof tU}function tV(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function tJ(e,t){let r=e._controlledTransformStream;if(!tY(r))throw TypeError("Readable side is not in a state that permits enqueue");try{r._readablePulling=!1;try{r._readableController.enqueue(t)}catch(e){throw tZ(r,e),e}}catch(e){throw tW(r,e),r._readableStoredError}!(tY(r)&&(r._readablePulling||t0(r)>0))!==r._backpressure&&tz(r,!0)}function tK(e,t){return h(e._transformAlgorithm(t),void 0,t=>{throw tF(e._controlledTransformStream,t),t})}function tX(e){return TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function tG(e){return TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}function tY(e){return!e._readableCloseRequested&&"readable"===e._readableState}function tQ(e){e._readableState="closed",e._readableCloseRequested=!0,e._readableController.close()}function tZ(e,t){"readable"===e._readableState&&(e._readableState="errored",e._readableStoredError=t),e._readableController.error(t)}function t0(e){return e._readableController.desiredSize}function t1(e,t){"writable"!==e._writableState?t3(e):t2(e,t)}function t2(e,t){e._writableState="erroring",e._writableStoredError=t,!e._writableHasInFlightOperation&&e._writableStarted&&t3(e)}function t3(e){e._writableState="errored"}function t4(e){"erroring"===e._writableState&&t3(e)}Object.defineProperties(tU.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),o(tU.prototype.enqueue,"enqueue"),o(tU.prototype.error,"error"),o(tU.prototype.terminate,"terminate"),"symbol"==typeof n.toStringTag&&Object.defineProperty(tU.prototype,n.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});var t6,t8,t5,t9=r(3245);async function*t7(e){let t=e.byteOffset+e.byteLength,r=e.byteOffset;for(;r!==t;){let n=Math.min(t-r,65536),s=e.buffer.slice(r,r+n);r+=s.byteLength,yield new Uint8Array(s)}}async function*re(e){let t=0;for(;t!==e.size;){let r=e.slice(t,Math.min(e.size,t+65536)),n=await r.arrayBuffer();t+=n.byteLength,yield new Uint8Array(n)}}async function*rt(e,t=!1){for(let r of e)ArrayBuffer.isView(r)?t?yield*t7(r):yield r:(0,t9.T)(r.stream)?yield*r.stream():yield*re(r)}var rr=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)},rn=function(e,t,r,n,s){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?s.call(e,r):s?s.value=r:t.set(e,r),r};class rs{constructor(e=[],t={}){if(t6.set(this,[]),t8.set(this,""),t5.set(this,0),null!=t||(t={}),"object"!=typeof e||null===e)throw TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(!(0,t9.T)(e[Symbol.iterator]))throw TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if("object"!=typeof t&&!(0,t9.T)(t))throw TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");let r=new TextEncoder;for(let t of e){let e;e=ArrayBuffer.isView(t)?new Uint8Array(t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength)):t instanceof ArrayBuffer?new Uint8Array(t.slice(0)):t instanceof rs?t:r.encode(String(t)),rn(this,t5,rr(this,t5,"f")+(ArrayBuffer.isView(e)?e.byteLength:e.size),"f"),rr(this,t6,"f").push(e)}let n=void 0===t.type?"":String(t.type);rn(this,t8,/^[\x20-\x7E]*$/.test(n)?n:"","f")}static[(t6=new WeakMap,t8=new WeakMap,t5=new WeakMap,Symbol.hasInstance)](e){return!!(e&&"object"==typeof e&&(0,t9.T)(e.constructor)&&((0,t9.T)(e.stream)||(0,t9.T)(e.arrayBuffer))&&/^(Blob|File)$/.test(e[Symbol.toStringTag]))}get type(){return rr(this,t8,"f")}get size(){return rr(this,t5,"f")}slice(e,t,r){return new rs(function*(e,t,r=0,n){null!=n||(n=t);let s=r<0?Math.max(t+r,0):Math.min(r,t),i=n<0?Math.max(t+n,0):Math.min(n,t),o=Math.max(i-s,0),a=0;for(let t of e){if(a>=o)break;let e=ArrayBuffer.isView(t)?t.byteLength:t.size;if(s&&e<=s)s-=e,i-=e;else{let r;ArrayBuffer.isView(t)?a+=(r=t.subarray(s,Math.min(e,i))).byteLength:a+=(r=t.slice(s,Math.min(e,i))).size,i-=e,s=0,yield r}}}(rr(this,t6,"f"),this.size,e,t),{type:r})}async text(){let e=new TextDecoder,t="";for await(let r of rt(rr(this,t6,"f")))t+=e.decode(r,{stream:!0});return t+e.decode()}async arrayBuffer(){let e=new Uint8Array(this.size),t=0;for await(let r of rt(rr(this,t6,"f")))e.set(r,t),t+=r.length;return e.buffer}stream(){let e=rt(rr(this,t6,"f"),!0);return new tS({async pull(t){let{value:r,done:n}=await e.next();if(n)return queueMicrotask(()=>t.close());t.enqueue(r)},async cancel(){await e.return()}})}get[Symbol.toStringTag](){return"Blob"}}Object.defineProperties(rs.prototype,{type:{enumerable:!0},size:{enumerable:!0},slice:{enumerable:!0},stream:{enumerable:!0},text:{enumerable:!0},arrayBuffer:{enumerable:!0}})},8990:(e,t,r)=>{"use strict";let n=r(1630).Agent,s=r(538),i=r(8354).debuglog("agentkeepalive"),{INIT_SOCKET:o,CURRENT_ID:a,CREATE_ID:l,SOCKET_CREATED_TIME:c,SOCKET_NAME:u,SOCKET_REQUEST_COUNT:d,SOCKET_REQUEST_FINISHED_COUNT:h}=r(2698),f=1,p=parseInt(process.version.split(".",1)[0].substring(1));function m(e){console.log("[agentkeepalive:deprecated] %s",e)}p>=11&&p<=12?f=2:p>=13&&(f=3);class g extends n{constructor(e){(e=e||{}).keepAlive=!1!==e.keepAlive,void 0===e.freeSocketTimeout&&(e.freeSocketTimeout=4e3),e.keepAliveTimeout&&(m("options.keepAliveTimeout is deprecated, please use options.freeSocketTimeout instead"),e.freeSocketTimeout=e.keepAliveTimeout,delete e.keepAliveTimeout),e.freeSocketKeepAliveTimeout&&(m("options.freeSocketKeepAliveTimeout is deprecated, please use options.freeSocketTimeout instead"),e.freeSocketTimeout=e.freeSocketKeepAliveTimeout,delete e.freeSocketKeepAliveTimeout),void 0===e.timeout&&(e.timeout=Math.max(2*e.freeSocketTimeout,8e3)),e.timeout=s(e.timeout),e.freeSocketTimeout=s(e.freeSocketTimeout),e.socketActiveTTL=e.socketActiveTTL?s(e.socketActiveTTL):0,super(e),this[a]=0,this.createSocketCount=0,this.createSocketCountLastCheck=0,this.createSocketErrorCount=0,this.createSocketErrorCountLastCheck=0,this.closeSocketCount=0,this.closeSocketCountLastCheck=0,this.errorSocketCount=0,this.errorSocketCountLastCheck=0,this.requestCount=0,this.requestCountLastCheck=0,this.timeoutSocketCount=0,this.timeoutSocketCountLastCheck=0,this.on("free",e=>{let t=this.calcSocketTimeout(e);t>0&&e.timeout!==t&&e.setTimeout(t)})}get freeSocketKeepAliveTimeout(){return m("agent.freeSocketKeepAliveTimeout is deprecated, please use agent.options.freeSocketTimeout instead"),this.options.freeSocketTimeout}get timeout(){return m("agent.timeout is deprecated, please use agent.options.timeout instead"),this.options.timeout}get socketActiveTTL(){return m("agent.socketActiveTTL is deprecated, please use agent.options.socketActiveTTL instead"),this.options.socketActiveTTL}calcSocketTimeout(e){let t=this.options.freeSocketTimeout,r=this.options.socketActiveTTL;if(r){let n=r-(Date.now()-e[c]);if(n<=0)return n;t&&n<t&&(t=n)}if(t)return e.freeSocketTimeout||e.freeSocketKeepAliveTimeout||t}keepSocketAlive(e){let t=super.keepSocketAlive(e);if(!t)return t;let r=this.calcSocketTimeout(e);return void 0===r||(r<=0?(i("%s(requests: %s, finished: %s) free but need to destroy by TTL, request count %s, diff is %s",e[u],e[d],e[h],r),!1):(e.timeout!==r&&e.setTimeout(r),!0))}reuseSocket(...e){super.reuseSocket(...e);let t=e[0];e[1].reusedSocket=!0;let r=this.options.timeout;y(t)!==r&&(t.setTimeout(r),i("%s reset timeout to %sms",t[u],r)),t[d]++,i("%s(requests: %s, finished: %s) reuse on addRequest, timeout %sms",t[u],t[d],t[h],y(t))}[l](){let e=this[a]++;return this[a]===Number.MAX_SAFE_INTEGER&&(this[a]=0),e}[o](e,t){t.timeout&&(y(e)||e.setTimeout(t.timeout)),this.options.keepAlive&&e.setNoDelay(!0),this.createSocketCount++,this.options.socketActiveTTL&&(e[c]=Date.now()),e[u]=`sock[${this[l]()}#${t._agentKey}]`.split("-----BEGIN",1)[0],e[d]=1,e[h]=0,function(e,t,r){function n(){if(!t._httpMessage&&1===t[d])return;t[h]++,e.requestCount++,i("%s(requests: %s, finished: %s) free",t[u],t[d],t[h]);let n=e.getName(r);t.writable&&e.requests[n]&&e.requests[n].length&&(t[d]++,i("%s(requests: %s, finished: %s) will be reuse on agent free event",t[u],t[d],t[h]))}function s(r){i("%s(requests: %s, finished: %s) close, isError: %s",t[u],t[d],t[h],r),e.closeSocketCount++}function o(){let n=t.listeners("timeout").length,s=y(t),o=t._httpMessage,a=o&&o.listeners("timeout").length||0;i("%s(requests: %s, finished: %s) timeout after %sms, listeners %s, defaultTimeoutListenerCount %s, hasHttpRequest %s, HttpRequest timeoutListenerCount %s",t[u],t[d],t[h],s,n,f,!!o,a),i.enabled&&i("timeout listeners: %s",t.listeners("timeout").map(e=>e.name).join(", ")),e.timeoutSocketCount++;let l=e.getName(r);if(e.freeSockets[l]&&-1!==e.freeSockets[l].indexOf(t))t.destroy(),e.removeSocket(t,r),i("%s is free, destroy quietly",t[u]);else if(0===a){let n=Error("Socket timeout");n.code="ERR_SOCKET_TIMEOUT",n.timeout=s,t.destroy(n),e.removeSocket(t,r),i("%s destroy with timeout error",t[u])}}function a(r){let n=t.listeners("error").length;i("%s(requests: %s, finished: %s) error: %s, listenerCount: %s",t[u],t[d],t[h],r,n),e.errorSocketCount++,1===n&&(i("%s emit uncaught error event",t[u]),t.removeListener("error",a),t.emit("error",r))}i("%s create, timeout %sms",t[u],y(t)),t.on("free",n),t.on("close",s),t.on("timeout",o),t.on("error",a),t.on("agentRemove",function e(){i("%s(requests: %s, finished: %s) agentRemove",t[u],t[d],t[h]),t.removeListener("close",s),t.removeListener("error",a),t.removeListener("free",n),t.removeListener("timeout",o),t.removeListener("agentRemove",e)})}(this,e,t)}createConnection(e,t){let r=!1,n=(n,s)=>{if(!r){if(r=!0,n)return this.createSocketErrorCount++,t(n);this[o](s,e),t(n,s)}},s=super.createConnection(e,n);return s&&n(null,s),s}get statusChanged(){let e=this.createSocketCount!==this.createSocketCountLastCheck||this.createSocketErrorCount!==this.createSocketErrorCountLastCheck||this.closeSocketCount!==this.closeSocketCountLastCheck||this.errorSocketCount!==this.errorSocketCountLastCheck||this.timeoutSocketCount!==this.timeoutSocketCountLastCheck||this.requestCount!==this.requestCountLastCheck;return e&&(this.createSocketCountLastCheck=this.createSocketCount,this.createSocketErrorCountLastCheck=this.createSocketErrorCount,this.closeSocketCountLastCheck=this.closeSocketCount,this.errorSocketCountLastCheck=this.errorSocketCount,this.timeoutSocketCountLastCheck=this.timeoutSocketCount,this.requestCountLastCheck=this.requestCount),e}getCurrentStatus(){return{createSocketCount:this.createSocketCount,createSocketErrorCount:this.createSocketErrorCount,closeSocketCount:this.closeSocketCount,errorSocketCount:this.errorSocketCount,timeoutSocketCount:this.timeoutSocketCount,requestCount:this.requestCount,freeSockets:b(this.freeSockets),sockets:b(this.sockets),requests:b(this.requests)}}}function y(e){return e.timeout||e._idleTimeout}function b(e){let t={};for(let r in e)t[r]=e[r].length;return t}e.exports=g},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9138:(e,t,r)=>{Promise.resolve().then(r.bind(r,2367))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9532:(e,t,r)=>{"use strict";let n=r(5591).Agent,s=r(8990),{INIT_SOCKET:i,CREATE_HTTPS_CONNECTION:o}=r(2698);class a extends s{constructor(e){super(e),this.defaultPort=443,this.protocol="https:",this.maxCachedSessions=this.options.maxCachedSessions,void 0===this.maxCachedSessions&&(this.maxCachedSessions=100),this._sessionCache={map:{},list:[]}}createConnection(e,t){let r=this[o](e,t);return this[i](r,e),r}}a.prototype[o]=n.prototype.createConnection,["getName","_getSession","_cacheSession","_evictSession"].forEach(function(e){"function"==typeof n.prototype[e]&&(a.prototype[e]=n.prototype[e])}),e.exports=a},9551:e=>{"use strict";e.exports=require("url")},9733:(e,t,r)=>{"use strict";e.exports=r(907)},9983:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(2579);class AbortSignal extends n.EventTarget{constructor(){throw super(),TypeError("AbortSignal cannot be constructed directly")}get aborted(){let e=s.get(this);if("boolean"!=typeof e)throw TypeError(`Expected 'this' to be an 'AbortSignal' object, but got ${this===null?"null":typeof this}`);return e}}n.defineEventAttribute(AbortSignal.prototype,"abort");let s=new WeakMap;Object.defineProperties(AbortSignal.prototype,{aborted:{enumerable:!0}}),"function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(AbortSignal.prototype,Symbol.toStringTag,{configurable:!0,value:"AbortSignal"});class i{constructor(){o.set(this,function(){let e=Object.create(AbortSignal.prototype);return n.EventTarget.call(e),s.set(e,!1),e}())}get signal(){return a(this)}abort(){var e;e=a(this),!1===s.get(e)&&(s.set(e,!0),e.dispatchEvent({type:"abort"}))}}let o=new WeakMap;function a(e){let t=o.get(e);if(null==t)throw TypeError(`Expected 'this' to be an 'AbortController' object, but got ${null===e?"null":typeof e}`);return t}Object.defineProperties(i.prototype,{signal:{enumerable:!0},abort:{enumerable:!0}}),"function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(i.prototype,Symbol.toStringTag,{configurable:!0,value:"AbortController"}),t.AbortController=i,t.AbortSignal=AbortSignal,t.default=i,e.exports=i,e.exports.AbortController=e.exports.default=i,e.exports.AbortSignal=AbortSignal}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,373,789],()=>r(1306));module.exports=n})();