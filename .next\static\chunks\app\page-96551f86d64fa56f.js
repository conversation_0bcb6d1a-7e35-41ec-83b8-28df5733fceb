(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1198:(e,s,t)=>{Promise.resolve().then(t.bind(t,6619))},6619:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(5155),a=t(2115);function l(e){let{onSendMessage:s,disabled:t=!1}=e,[l,n]=(0,a.useState)(""),[i,d]=(0,a.useState)(!1),c=async e=>{e.preventDefault(),!l.trim()||i||t||(d(!0),await s(l.trim()),n(""),d(!1))};return(0,r.jsxs)("form",{onSubmit:c,className:"flex space-x-2",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("textarea",{value:l,onChange:e=>n(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||t||(e.preventDefault(),c(e))},placeholder:t?"Chat er midlertidigt utilg\xe6ngelig...":"Skriv dit forsikringssp\xf8rgsm\xe5l her...",className:"input-field resize-none h-12",rows:1,disabled:i||t})}),(0,r.jsx)("button",{type:"submit",disabled:!l.trim()||i||t,className:"btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed",children:i?(0,r.jsxs)("svg",{className:"animate-spin h-5 w-5",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):"Send"})]})}class n extends a.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("ErrorBoundary caught an error:",e,s)}render(){if(this.state.hasError){let e=this.props.fallback||i;return(0,r.jsx)(e,{error:this.state.error,resetError:this.resetError})}return this.props.children}constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}}function i(e){let{error:s,resetError:t}=e;return(0,r.jsxs)("div",{className:"card max-w-2xl mx-auto text-center",children:[(0,r.jsx)("div",{className:"text-red-500 text-4xl mb-4",children:"⚠️"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Der opstod en uventet fejl"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Beklager, noget gik galt. Pr\xf8v venligst igen eller kontakt support hvis problemet forts\xe6tter."}),!1,(0,r.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,r.jsx)("button",{onClick:t,className:"btn-primary",children:"Pr\xf8v igen"}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"btn-secondary",children:"Genindl\xe6s siden"})]})]})}function d(e){let{resetError:s}=e;return(0,r.jsxs)("div",{className:"border-2 border-red-200 rounded-lg p-6 text-center bg-red-50",children:[(0,r.jsx)("div",{className:"text-red-500 text-2xl mb-3",children:"\uD83E\uDD16\uD83D\uDCA5"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-red-800 mb-2",children:"Chat fejl"}),(0,r.jsx)("p",{className:"text-red-700 mb-4",children:"AI-assistenten kunne ikke svare. Dette kan skyldes netv\xe6rksproblemer eller API-fejl."}),(0,r.jsx)("button",{onClick:s,className:"bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors",children:"Pr\xf8v chat igen"})]})}function c(e){let{size:s="md",text:t="Indl\xe6ser...",className:a=""}=e;return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(a),children:[(0,r.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," animate-spin"),children:(0,r.jsxs)("svg",{className:"w-full h-full text-insurance-blue",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),t&&(0,r.jsx)("p",{className:"mt-2 text-gray-600 ".concat({sm:"text-sm",md:"text-base",lg:"text-lg"}[s]),children:t})]})}function o(){return(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-insurance-blue rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-insurance-blue rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,r.jsx)("div",{className:"w-2 h-2 bg-insurance-blue rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),(0,r.jsx)("span",{className:"text-sm",children:"AI t\xe6nker..."})]})}function m(){return(0,r.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,r.jsx)(c,{size:"lg",text:"Behandler dokument..."})})}function x(){let[e,s]=(0,a.useState)([{id:"1",content:"Hej! Jeg er din AI forsikringsassistent. Hvordan kan jeg hj\xe6lpe dig i dag?",role:"assistant",timestamp:new Date}]),[t,i]=(0,a.useState)(!1),[c,m]=(0,a.useState)(null),x=async t=>{let r={id:Date.now().toString(),content:t,role:"user",timestamp:new Date};s(e=>[...e,r]),i(!0),m(null);try{let t=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...e,r].map(e=>({role:e.role,content:e.content})),userDocuments:[]})}),l=await t.json();if(!t.ok){var a;if(500===t.status&&(null==(a=l.error)?void 0:a.includes("API key")))throw Error("OpenAI API n\xf8gle er ikke konfigureret. Kontakt administrator.");if(401===t.status)throw Error("Ugyldig API n\xf8gle. Kontakt administrator.");if(429===t.status)throw Error("For mange foresp\xf8rgsler. Pr\xf8v igen om lidt.");throw Error(l.error||"Ukendt fejl fra serveren")}let n={id:(Date.now()+1).toString(),content:l.message,role:"assistant",timestamp:new Date};s(e=>[...e,n])}catch(r){console.error("Error sending message:",r);let e=r instanceof Error?r.message:"Der opstod en uventet fejl";m(e);let t={id:(Date.now()+1).toString(),content:"❌ ".concat(e),role:"assistant",timestamp:new Date};s(e=>[...e,t])}finally{i(!1)}};return(0,r.jsx)(n,{fallback:d,children:(0,r.jsxs)("div",{className:"card max-w-4xl mx-auto",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Chat med AI Forsikringsassistent"}),c&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-red-100 border border-red-300 rounded-lg",children:(0,r.jsxs)("p",{className:"text-red-700 text-sm",children:[(0,r.jsx)("strong",{children:"Fejl:"})," ",c]})}),(0,r.jsxs)("div",{className:"h-96 overflow-y-auto mb-4 border rounded-lg p-4 bg-gray-50",children:[e.map(e=>(0,r.jsx)("div",{className:"mb-4 ".concat("user"===e.role?"text-right":"text-left"),children:(0,r.jsxs)("div",{className:"inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ".concat("user"===e.role?"bg-insurance-blue text-white":"bg-white text-gray-900 border"),children:[(0,r.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:e.content}),(0,r.jsx)("p",{className:"text-xs opacity-70 mt-1",children:e.timestamp.toLocaleTimeString("da-DK",{hour:"2-digit",minute:"2-digit"})})]})},e.id)),t&&(0,r.jsx)("div",{className:"mb-4 text-left",children:(0,r.jsx)("div",{className:"inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-white border",children:(0,r.jsx)(o,{})})})]}),(0,r.jsx)(l,{onSendMessage:x,disabled:t})]})})}function u(e){let{onDocumentParsed:s}=e,[t,l]=(0,a.useState)(!1),[n,i]=(0,a.useState)(null),d=async e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){if(r.size>0xa00000){i("Filen er for stor. Maksimal st\xf8rrelse er 10MB."),e.target.value="";return}l(!0),i(null);try{let e="";if("application/pdf"===r.type)e="[PDF Dokument: ".concat(r.name,"]\nDette er en PDF fil. Indhold vil blive behandlet n\xe5r backend PDF parsing er implementeret.\n\nFilst\xf8rrelse: ").concat((r.size/1024/1024).toFixed(2)," MB");else if("text/plain"===r.type)(e=await r.text()).length>5e4&&(e=e.substring(0,5e4)+"\n\n[Tekst afkortet - for lang til visning]");else if(r.type.startsWith("image/"))e="[Billede: ".concat(r.name,"]\nDette er et billede. OCR behandling vil blive implementeret for at udtr\xe6kke tekst.\n\nFilst\xf8rrelse: ").concat((r.size/1024/1024).toFixed(2)," MB");else throw Error("Ikke-underst\xf8ttet filtype. Upload venligst PDF, tekst eller billede filer.");let t=c(r.name,e);s({name:r.name,content:e,type:t})}catch(e){console.error("Upload error:",e),i(e instanceof Error?e.message:"Der opstod en fejl ved upload")}finally{l(!1),e.target.value=""}}},c=(e,s)=>{let t=e.toLowerCase()+" "+s.toLowerCase();return t.includes("police")||t.includes("forsikring")||t.includes("d\xe6kning")?"insurance_policy":t.includes("skade")||t.includes("anmeldelse")||t.includes("erstatning")?"claim_document":t.includes("korrespondance")||t.includes("brev")||t.includes("mail")?"correspondence":"general_document"};return t?(0,r.jsx)("div",{className:"border-2 border-dashed border-insurance-blue rounded-lg p-6",children:(0,r.jsx)(m,{})}):(0,r.jsx)("div",{className:"border-2 border-dashed rounded-lg p-6 text-center transition-colors ".concat(n?"border-red-300 bg-red-50":"border-gray-300 hover:border-insurance-blue"),children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"mx-auto w-12 h-12 ".concat(n?"text-red-400":"text-gray-400"),children:n?(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 48 48",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"})}):(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 48 48",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"})})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"file-upload",className:"cursor-pointer",children:[(0,r.jsx)("span",{className:"text-sm font-medium hover:opacity-80 ".concat(n?"text-red-600":"text-insurance-blue hover:text-insurance-dark"),children:n?"Pr\xf8v igen":"Upload forsikringsdokument"}),(0,r.jsx)("input",{id:"file-upload",name:"file-upload",type:"file",className:"sr-only",accept:".pdf,.txt,.jpg,.jpeg,.png",onChange:d,disabled:t})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"PDF, tekst eller billede filer op til 10MB"})]}),t&&(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsxs)("svg",{className:"animate-spin h-4 w-4 text-insurance-blue",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Behandler dokument..."})]}),n&&(0,r.jsx)("div",{className:"text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2",children:n})]})})}function h(e){let{documents:s,onDocumentSelect:t,highlightTerms:l=[]}=e,[n,i]=(0,a.useState)(s.length>0?s[0]:null),[d,c]=(0,a.useState)(""),o=e=>{i(e),null==t||t(e)},m=e=>{switch(e){case"insurance_policy":return"\uD83D\uDCCB";case"claim_document":return"\uD83D\uDD27";case"correspondence":return"✉️";default:return"\uD83D\uDCC4"}},x=e=>{switch(e){case"insurance_policy":return"Forsikringspolice";case"claim_document":return"Skadeanmeldelse";case"correspondence":return"Korrespondance";default:return"Dokument"}};return 0===s.length?(0,r.jsxs)("div",{className:"card text-center py-8",children:[(0,r.jsx)("div",{className:"text-gray-400 text-4xl mb-4",children:"\uD83D\uDCC4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Ingen dokumenter uploadet"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Upload forsikringsdokumenter for at f\xe5 personaliseret hj\xe6lp"})]}):(0,r.jsx)("div",{className:"card",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,r.jsxs)("div",{className:"lg:w-1/3",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:["Mine Dokumenter (",s.length,")"]}),(0,r.jsx)("div",{className:"space-y-2",children:s.map((e,s)=>(0,r.jsx)("button",{onClick:()=>o(e),className:"w-full text-left p-3 rounded-lg border transition-colors ".concat((null==n?void 0:n.name)===e.name?"border-insurance-blue bg-insurance-light":"border-gray-200 hover:border-gray-300"),children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)("span",{className:"text-lg",children:m(e.type)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:x(e.type)})]})]})},s))})]}),(0,r.jsx)("div",{className:"lg:w-2/3",children:n&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:n.name}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("input",{type:"text",placeholder:"S\xf8g i dokument...",value:d,onChange:e=>c(e.target.value),className:"text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-1 focus:ring-insurance-blue focus:border-insurance-blue"})})]}),(0,r.jsx)("div",{className:"bg-gray-50 border rounded-lg p-4 max-h-96 overflow-y-auto",children:(0,r.jsx)("div",{className:"text-sm text-gray-700 whitespace-pre-wrap leading-relaxed",dangerouslySetInnerHTML:{__html:((e,s)=>{if(!s)return e;let t=RegExp("(".concat(s,")"),"gi");return e.replace(t,'<mark class="bg-blue-200 px-1 rounded font-semibold">$1</mark>')})(((e,s)=>{if(0===s.length)return e;let t=e;return s.forEach(e=>{let s=RegExp("(".concat(e,")"),"gi");t=t.replace(s,'<mark class="bg-yellow-200 px-1 rounded">$1</mark>')}),t})(n.content,l),d)}})}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between text-xs text-gray-500",children:[(0,r.jsxs)("span",{children:["Type: ",x(n.type)]}),n.uploadDate&&(0,r.jsxs)("span",{children:["Uploadet: ",n.uploadDate.toLocaleDateString("da-DK")]})]})]})})]})})}function g(){let[e,s]=(0,a.useState)([]),[t,l]=(0,a.useState)(null),[n,i]=(0,a.useState)("chat"),d=e=>{let t={...e,uploadDate:new Date};s(e=>[...e,t])};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8 text-center",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Velkommen til AI Forsikringsguiden"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"Intelligent forsikringsr\xe5dgivning p\xe5 dansk. F\xe5 svar p\xe5 dine sp\xf8rgsm\xe5l, analyser dokumenter, og f\xe5 hj\xe6lp til forsikringssp\xf8rgsm\xe5l."})]}),(0,r.jsxs)("div",{className:"flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm border w-fit mx-auto",children:[(0,r.jsx)("button",{onClick:()=>i("chat"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat("chat"===n?"bg-insurance-blue text-white":"text-gray-600 hover:text-gray-900"),children:"\uD83D\uDCAC Chat"}),(0,r.jsx)("button",{onClick:()=>i("dashboard"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat("dashboard"===n?"bg-insurance-blue text-white":"text-gray-600 hover:text-gray-900"),children:"\uD83D\uDCCA Dashboard"}),(0,r.jsxs)("button",{onClick:()=>i("documents"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat("documents"===n?"bg-insurance-blue text-white":"text-gray-600 hover:text-gray-900"),children:["\uD83D\uDCC1 Dokumenter (",e.length,")"]})]}),"dashboard"===n&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-8 text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"AI Insights Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Avanceret dashboard med proaktive AI-indsigter kommer snart. Kr\xe6ver Supabase setup og brugerautentificering."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDEE1️"}),(0,r.jsx)("h3",{className:"font-semibold",children:"Coverage Analysis"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Analyse af d\xe6kningshuller"})]}),(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCB0"}),(0,r.jsx)("h3",{className:"font-semibold",children:"Cost Optimization"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Besparelsesmuligheder"})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCC5"}),(0,r.jsx)("h3",{className:"font-semibold",children:"Renewal Reminders"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"P\xe5mindelser om fornyelse"})]})]})]}),"documents"===n&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Upload Nyt Dokument"}),(0,r.jsx)(u,{onDocumentParsed:d})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Dine Dokumenter"}),(0,r.jsx)(h,{documents:e})]})]}),"chat"===n&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-300px)]",children:[(0,r.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Hurtig Upload"}),(0,r.jsx)(u,{onDocumentParsed:d})]}),e.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Seneste Dokumenter"}),(0,r.jsx)("div",{className:"space-y-2",children:e.slice(0,3).map((e,s)=>(0,r.jsxs)("button",{onClick:()=>l(e),className:"w-full text-left p-2 rounded border hover:bg-gray-50 text-sm ".concat((null==t?void 0:t.name)===e.name?"bg-insurance-blue/10 border-insurance-blue":""),children:[(0,r.jsx)("div",{className:"font-medium truncate",children:e.name}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:e.type})]},s))})]})]}),(0,r.jsx)("div",{className:"lg:col-span-3 flex flex-col",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border flex-1 flex flex-col",children:[(0,r.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"AI Forsikringsr\xe5dgiver"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Still sp\xf8rgsm\xe5l om forsikring eller upload dokumenter til analyse"})]}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,r.jsx)(x,{})})]})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(1198)),_N_E=e.O()}]);