exports.id=789,exports.ids=[789],exports.modules={58:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=58,e.exports=r},1135:()=>{},1219:(e,r,s)=>{"use strict";s.d(r,{A:()=>o,AuthProvider:()=>a});var t=s(687),i=s(3210),n=s(9481);let l=(0,i.createContext)(void 0);function a({children:e}){let[r,s]=(0,i.useState)(null),[a,o]=(0,i.useState)(null),[c,d]=(0,i.useState)(null),[h,u]=(0,i.useState)(!0),x=(0,n.UU)(),m=async e=>{try{let{data:r,error:s}=await x.from("user_profiles").select("*").eq("id",e).single();if(s&&"PGRST116"!==s.code)return void console.error("Error fetching user profile:",s);o(r)}catch(e){console.error("Error fetching user profile:",e)}},g=async(e,r)=>{try{let{error:s}=await x.auth.signInWithPassword({email:e,password:r});if(s)return{error:s.message};return{}}catch(e){return{error:"An unexpected error occurred"}}},f=async(e,r,s)=>{try{let{data:t,error:i}=await x.auth.signUp({email:e,password:r,options:{data:{full_name:s?.full_name||""}}});if(i)return{error:i.message};if(t.user){let{error:e}=await x.from("user_profiles").insert({id:t.user.id,email:t.user.email,full_name:s?.full_name||null,phone:s?.phone||null,preferences:{},risk_profile:{}});e&&console.error("Error creating user profile:",e)}return{}}catch(e){return{error:"An unexpected error occurred"}}},v=async()=>{await x.auth.signOut()},p=async e=>{if(!r)return{error:"No user logged in"};try{let{error:s}=await x.from("user_profiles").update({...e,updated_at:new Date().toISOString()}).eq("id",r.id);if(s)return{error:s.message};return await m(r.id),{}}catch(e){return{error:"An unexpected error occurred"}}};return(0,t.jsx)(l.Provider,{value:{user:r,profile:a,session:c,loading:h,signIn:g,signUp:f,signOut:v,updateProfile:p},children:e})}function o(){let e=(0,i.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2958:(e,r,s)=>{Promise.resolve().then(s.bind(s,8785))},4431:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d,metadata:()=>c});var t=s(7413),i=s(4919),n=s.n(i),l=s(2400),a=s.n(l);s(1135);var o=s(8785);let c={title:"AI Forsikringsguiden | Intelligent dansk forsikringsr\xe5dgivning",description:"F\xe5 ekspert forsikringsr\xe5dgivning med AI. Upload dokumenter, stil sp\xf8rgsm\xe5l, og f\xe5 personlig hj\xe6lp til forsikringssp\xf8rgsm\xe5l p\xe5 dansk.",keywords:"forsikring, AI, r\xe5dgivning, danmark, bilforsikring, husejerforsikring, indboforsikring",authors:[{name:"AI Forsikringsguiden Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"AI Forsikringsguiden",description:"Intelligent dansk forsikringsr\xe5dgivning med AI",type:"website",locale:"da_DK",siteName:"AI Forsikringsguiden"}};function d({children:e}){return(0,t.jsx)("html",{lang:"da",children:(0,t.jsx)("body",{className:`${n().variable} ${a().variable} antialiased bg-gray-50 min-h-screen`,children:(0,t.jsx)(o.AuthProvider,{children:(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,t.jsx)("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-insurance-blue rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Forsikringsguiden"})]})}),(0,t.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,t.jsx)("a",{href:"/",className:"text-gray-600 hover:text-insurance-blue transition-colors",children:"Hjem"}),(0,t.jsx)("a",{href:"/chat",className:"text-gray-600 hover:text-insurance-blue transition-colors",children:"Chat"}),(0,t.jsx)("a",{href:"/dashboard",className:"text-gray-600 hover:text-insurance-blue transition-colors",children:"Dashboard"}),(0,t.jsx)("a",{href:"/policies",className:"text-gray-600 hover:text-insurance-blue transition-colors",children:"Forsikringer"}),(0,t.jsx)("a",{href:"/documents",className:"text-gray-600 hover:text-insurance-blue transition-colors",children:"Dokumenter"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)("button",{className:"text-gray-600 hover:text-insurance-blue",title:"Brugerindstillinger","aria-label":"\xc5bn brugerindstillinger",children:(0,t.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-5-5 5-5m-5 5H9"})})})})]})})}),(0,t.jsx)("main",{className:"flex-1",children:e}),(0,t.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-auto",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-4",children:"AI Forsikringsguiden"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Intelligent forsikringsr\xe5dgivning p\xe5 dansk med respekt for dit privatliv."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-4",children:"Tjenester"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/chat",className:"hover:text-insurance-blue",children:"AI Chat"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/documents",className:"hover:text-insurance-blue",children:"Dokumentanalyse"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/comparison",className:"hover:text-insurance-blue",children:"Prissammenligning"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/claims",className:"hover:text-insurance-blue",children:"Skadeh\xe5ndtering"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-4",children:"Support"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/help",className:"hover:text-insurance-blue",children:"Hj\xe6lp & FAQ"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/contact",className:"hover:text-insurance-blue",children:"Kontakt os"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/feedback",className:"hover:text-insurance-blue",children:"Send feedback"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/status",className:"hover:text-insurance-blue",children:"Service status"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-4",children:"Juridisk"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/privacy",className:"hover:text-insurance-blue",children:"Privatlivspolitik"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/terms",className:"hover:text-insurance-blue",children:"Servicevilk\xe5r"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/gdpr",className:"hover:text-insurance-blue",children:"GDPR"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/cookies",className:"hover:text-insurance-blue",children:"Cookie politik"})})]})]})]}),(0,t.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"\xa9 2024 AI Forsikringsguiden. Alle rettigheder forbeholdes."}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-4 md:mt-0",children:[(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"Bygget med ❤️ i Danmark"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"Alle systemer k\xf8rer"})]})]})]})})]})})]})})})})}},4710:(e,r,s)=>{Promise.resolve().then(s.bind(s,1219))},7990:()=>{},8785:(e,r,s)=>{"use strict";s.d(r,{AuthProvider:()=>i});var t=s(2907);let i=(0,t.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI forsikrings guiden\\src\\lib\\auth\\AuthProvider.tsx","AuthProvider");(0,t.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI forsikrings guiden\\src\\lib\\auth\\AuthProvider.tsx","useAuth"),(0,t.registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI forsikrings guiden\\src\\lib\\auth\\AuthProvider.tsx","ProtectedRoute")},9481:(e,r,s)=>{"use strict";s.d(r,{UU:()=>l});var t=s(9384);let i=process.env.NEXT_PUBLIC_SUPABASE_URL,n=process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,l=()=>(0,t.createBrowserClient)(i,n)},9679:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},9727:()=>{},9927:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))}};