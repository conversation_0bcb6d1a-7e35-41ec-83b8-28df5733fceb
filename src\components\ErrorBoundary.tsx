'use client'

import React from 'react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>
}

export default class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    // TODO: Send error to monitoring service (Sentry, LogRocket, etc.)
    // logErrorToService(error, errorInfo)
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined })
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback
      return <FallbackComponent error={this.state.error!} resetError={this.resetError} />
    }

    return this.props.children
  }
}

// Default error fallback component
function DefaultErrorFallback({ error, resetError }: { error: Error; resetError: () => void }) {
  return (
    <div className="card max-w-2xl mx-auto text-center">
      <div className="text-red-500 text-4xl mb-4">⚠️</div>
      <h2 className="text-xl font-semibold text-gray-900 mb-4">
        Der opstod en uventet fejl
      </h2>
      <p className="text-gray-600 mb-6">
        Beklager, noget gik galt. Prøv venligst igen eller kontakt support hvis problemet fortsætter.
      </p>
      
      {process.env.NODE_ENV === 'development' && (
        <details className="text-left mb-6 p-4 bg-gray-100 rounded-lg">
          <summary className="cursor-pointer font-medium text-gray-700 mb-2">
            Tekniske detaljer (kun i udvikling)
          </summary>
          <pre className="text-sm text-red-600 whitespace-pre-wrap">
            {error.message}
            {error.stack && '\n\n' + error.stack}
          </pre>
        </details>
      )}
      
      <div className="flex gap-4 justify-center">
        <button 
          onClick={resetError}
          className="btn-primary"
        >
          Prøv igen
        </button>
        <button 
          onClick={() => window.location.reload()}
          className="btn-secondary"
        >
          Genindlæs siden
        </button>
      </div>
    </div>
  )
}

// Specific error fallback for chat
export function ChatErrorFallback({ error, resetError }: { error: Error; resetError: () => void }) {
  return (
    <div className="border-2 border-red-200 rounded-lg p-6 text-center bg-red-50">
      <div className="text-red-500 text-2xl mb-3">🤖💥</div>
      <h3 className="text-lg font-semibold text-red-800 mb-2">
        Chat fejl
      </h3>
      <p className="text-red-700 mb-4">
        AI-assistenten kunne ikke svare. Dette kan skyldes netværksproblemer eller API-fejl.
      </p>
      <button 
        onClick={resetError}
        className="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
      >
        Prøv chat igen
      </button>
    </div>
  )
}
