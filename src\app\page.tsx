'use client'

import Cha<PERSON><PERSON><PERSON>ow from '@/components/chat/ChatWindow'
import PDFUpload from '@/components/PDFUpload'
import DocumentViewer from '@/components/DocumentViewer'
import { useState } from 'react'

interface Document {
  name: string
  content: string
  type: string
  uploadDate: Date
}

export default function Home() {
  const [documents, setDocuments] = useState<Document[]>([])
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null)
  const [currentView, setCurrentView] = useState<'chat' | 'dashboard' | 'documents'>('chat')

  const handleDocumentParsed = (document: { name: string; content: string; type: string }) => {
    const newDocument: Document = {
      ...document,
      uploadDate: new Date()
    }
    setDocuments(prev => [...prev, newDocument])
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Welcome section */}
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Velkommen til AI Forsikringsguiden
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Intelligent forsikringsrådgivning på dansk. Få svar på dine spørgsmål,
            analyser dokumenter, og få hjælp til forsikringsspørgsmål.
          </p>
        </div>

        {/* View Toggle */}
        <div className="flex space-x-1 mb-6 bg-white rounded-lg p-1 shadow-sm border w-fit mx-auto">
          <button
            onClick={() => setCurrentView('chat')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              currentView === 'chat'
                ? 'bg-insurance-blue text-white'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            💬 Chat
          </button>
          <button
            onClick={() => setCurrentView('dashboard')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              currentView === 'dashboard'
                ? 'bg-insurance-blue text-white'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            📊 Dashboard
          </button>
          <button
            onClick={() => setCurrentView('documents')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              currentView === 'documents'
                ? 'bg-insurance-blue text-white'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            📁 Dokumenter ({documents.length})
          </button>
        </div>

        {/* Content */}
        {currentView === 'dashboard' && (
          <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">AI Insights Dashboard</h2>
            <p className="text-gray-600 mb-6">
              Avanceret dashboard med proaktive AI-indsigter kommer snart.
              Kræver Supabase setup og brugerautentificering.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="text-2xl mb-2">🛡️</div>
                <h3 className="font-semibold">Coverage Analysis</h3>
                <p className="text-sm text-gray-600">Analyse af dækningshuller</p>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="text-2xl mb-2">💰</div>
                <h3 className="font-semibold">Cost Optimization</h3>
                <p className="text-sm text-gray-600">Besparelsesmuligheder</p>
              </div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="text-2xl mb-2">📅</div>
                <h3 className="font-semibold">Renewal Reminders</h3>
                <p className="text-sm text-gray-600">Påmindelser om fornyelse</p>
              </div>
            </div>
          </div>
        )}

        {currentView === 'documents' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Upload Nyt Dokument
              </h2>
              <PDFUpload onDocumentParsed={handleDocumentParsed} />
            </div>

            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Dine Dokumenter
              </h2>
              <DocumentViewer documents={documents} />
            </div>
          </div>
        )}

        {currentView === 'chat' && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-300px)]">

            {/* Left sidebar - Quick upload */}
            <div className="lg:col-span-1 space-y-6">
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  Hurtig Upload
                </h2>
                <PDFUpload onDocumentParsed={handleDocumentParsed} />
              </div>

              {documents.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">
                    Seneste Dokumenter
                  </h2>
                  <div className="space-y-2">
                    {documents.slice(0, 3).map((doc, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedDocument(doc)}
                        className={`w-full text-left p-2 rounded border hover:bg-gray-50 text-sm ${
                          selectedDocument?.name === doc.name ? 'bg-insurance-blue/10 border-insurance-blue' : ''
                        }`}
                      >
                        <div className="font-medium truncate">{doc.name}</div>
                        <div className="text-xs text-gray-500">{doc.type}</div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Main chat area */}
            <div className="lg:col-span-3 flex flex-col">
              <div className="bg-white rounded-lg shadow-sm border flex-1 flex flex-col">
                <div className="p-6 border-b border-gray-200">
                  <h1 className="text-xl font-bold text-gray-900">
                    AI Forsikringsrådgiver
                  </h1>
                  <p className="text-gray-600 text-sm mt-1">
                    Still spørgsmål om forsikring eller upload dokumenter til analyse
                  </p>
                </div>

                <div className="flex-1 overflow-hidden">
                  <ChatWindow />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}