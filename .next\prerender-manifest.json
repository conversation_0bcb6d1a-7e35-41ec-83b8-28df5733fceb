{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "e9960304a97fc8fe64e0b171c3a10244", "previewModeSigningKey": "81f445a94c143aa5a53dfe6a6b4e34ecaf86773af3bced5caae91b318de03b83", "previewModeEncryptionKey": "0bf1dfd74337a11b870813a139d71e4f56be1a07db81ab76bd85596a576ee121"}}