{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "4c61fffd016d7fff72cf8547a3247850", "previewModeSigningKey": "f9494168177fd4d4d3f442fd9f4ea88242e1cbc2be796c376f96282e38683a79", "previewModeEncryptionKey": "394758a9d6dde4a068fd6e0b471e2ff71636d674bee47c8d1cbb98b340b1eef7"}}