{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "1de888670ad4dd3b96c69a03b079096e", "previewModeSigningKey": "719132356d3038a83a8f3d6e5cd13f873a7d2dd65c67ed9fff61b32426e13243", "previewModeEncryptionKey": "c75f2c239ddd7bac4e27fdb296e0fb46fb524618ec5ab2ec6c93b042d065f269"}}