1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[894,[],"ClientPageRoot"]
5:I[6619,["974","static/chunks/app/page-7d9e8904d14ed209.js"],"default"]
8:I[9665,[],"MetadataBoundary"]
a:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[6614,[],""]
:HL["/_next/static/css/033286cb7e57f891.css","style"]
0:{"P":null,"b":"WUTqOs9ezptkBLjFYHBc1","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/033286cb7e57f891.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"da","children":["$","body",null,{"className":"__className_e8ce0c antialiased bg-gray-50 min-h-screen","children":["$","div",null,{"className":"min-h-screen flex flex-col","children":[["$","header",null,{"className":"bg-white border-b border-gray-200 sticky top-0 z-50","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"flex items-center justify-between h-16","children":[["$","div",null,{"className":"flex items-center space-x-4","children":["$","div",null,{"className":"flex items-center space-x-2","children":[["$","div",null,{"className":"w-8 h-8 bg-insurance-blue rounded-lg flex items-center justify-center","children":["$","span",null,{"className":"text-white font-bold text-sm","children":"AI"}]}],["$","h1",null,{"className":"text-xl font-bold text-gray-900","children":"Forsikringsguiden"}]]}]}],["$","nav",null,{"className":"hidden md:flex items-center space-x-6","children":[["$","a",null,{"href":"/","className":"text-gray-600 hover:text-insurance-blue transition-colors","children":"Hjem"}],["$","a",null,{"href":"/chat","className":"text-gray-600 hover:text-insurance-blue transition-colors","children":"Chat"}],["$","a",null,{"href":"/dashboard","className":"text-gray-600 hover:text-insurance-blue transition-colors","children":"Dashboard"}],["$","a",null,{"href":"/policies","className":"text-gray-600 hover:text-insurance-blue transition-colors","children":"Forsikringer"}],["$","a",null,{"href":"/documents","className":"text-gray-600 hover:text-insurance-blue transition-colors","children":"Dokumenter"}]]}],["$","div",null,{"className":"flex items-center space-x-4","children":["$","button",null,{"className":"text-gray-600 hover:text-insurance-blue","title":"Brugerindstillinger","aria-label":"Åbn brugerindstillinger","children":["$","svg",null,{"className":"w-5 h-5","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M15 17h5l-5-5 5-5m-5 5H9"}]}]}]}]]}]}]}],["$","main",null,{"className":"flex-1","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","footer",null,{"className":"bg-white border-t border-gray-200 mt-auto","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8","children":[["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-4 gap-8","children":[["$","div",null,{"children":[["$","h3",null,{"className":"text-sm font-semibold text-gray-900 mb-4","children":"AI Forsikringsguiden"}],["$","p",null,{"className":"text-sm text-gray-600","children":"Intelligent forsikringsrådgivning på dansk med respekt for dit privatliv."}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-sm font-semibold text-gray-900 mb-4","children":"Tjenester"}],["$","ul",null,{"className":"space-y-2 text-sm text-gray-600","children":[["$","li",null,{"children":["$","a",null,{"href":"/chat","className":"hover:text-insurance-blue","children":"AI Chat"}]}],["$","li",null,{"children":["$","a",null,{"href":"/documents","className":"hover:text-insurance-blue","children":"Dokumentanalyse"}]}],["$","li",null,{"children":["$","a",null,{"href":"/comparison","className":"hover:text-insurance-blue","children":"Prissammenligning"}]}],["$","li",null,{"children":["$","a",null,{"href":"/claims","className":"hover:text-insurance-blue","children":"Skadehåndtering"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-sm font-semibold text-gray-900 mb-4","children":"Support"}],["$","ul",null,{"className":"space-y-2 text-sm text-gray-600","children":[["$","li",null,{"children":["$","a",null,{"href":"/help","className":"hover:text-insurance-blue","children":"Hjælp & FAQ"}]}],["$","li",null,{"children":["$","a",null,{"href":"/contact","className":"hover:text-insurance-blue","children":"Kontakt os"}]}],["$","li",null,{"children":["$","a",null,{"href":"/feedback","className":"hover:text-insurance-blue","children":"Send feedback"}]}],["$","li",null,{"children":["$","a",null,{"href":"/status","className":"hover:text-insurance-blue","children":"Service status"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-sm font-semibold text-gray-900 mb-4","children":"Juridisk"}],["$","ul",null,{"className":"space-y-2 text-sm text-gray-600","children":[["$","li",null,{"children":["$","a",null,{"href":"/privacy","className":"hover:text-insurance-blue","children":"Privatlivspolitik"}]}],["$","li",null,{"children":["$","a",null,{"href":"/terms","className":"hover:text-insurance-blue","children":"Servicevilkår"}]}],["$","li",null,{"children":["$","a",null,{"href":"/gdpr","className":"hover:text-insurance-blue","children":"GDPR"}]}],["$","li",null,{"children":["$","a",null,{"href":"/cookies","className":"hover:text-insurance-blue","children":"Cookie politik"}]}]]}]]}]]}],["$","div",null,{"className":"mt-8 pt-8 border-t border-gray-200","children":["$","div",null,{"className":"flex flex-col md:flex-row items-center justify-between","children":[["$","p",null,{"className":"text-sm text-gray-600","children":"© 2024 AI Forsikringsguiden. Alle rettigheder forbeholdes."}],["$","div",null,{"className":"flex items-center space-x-4 mt-4 md:mt-0","children":[["$","span",null,{"className":"text-xs text-gray-500","children":"Bygget med ❤️ i Danmark"}],["$","div",null,{"className":"flex items-center space-x-2","children":[["$","div",null,{"className":"w-2 h-2 bg-green-400 rounded-full"}],["$","span",null,{"className":"text-xs text-gray-500","children":"Alle systemer kører"}]]}]]}]]}]}]]}]}]]}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L4",null,{"Component":"$5","searchParams":{},"params":{},"promises":["$@6","$@7"]}],["$","$L8",null,{"children":"$L9"}],null,["$","$La",null,{"children":["$Lb","$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","g0HS0c8OJB3SdgD5gIs0v",{"children":[["$","$Lf",null,{"children":"$L10"}],null]}],null]}],false]],"m":"$undefined","G":["$11","$undefined"],"s":false,"S":true}
12:"$Sreact.suspense"
13:I[4911,[],"AsyncMetadata"]
6:{}
7:{}
9:["$","$12",null,{"fallback":null,"children":["$","$L13",null,{"promise":"$@14"}]}]
c:null
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
14:{"metadata":[["$","title","0",{"children":"AI Forsikringsguiden | Intelligent dansk forsikringsrådgivning"}],["$","meta","1",{"name":"description","content":"Få ekspert forsikringsrådgivning med AI. Upload dokumenter, stil spørgsmål, og få personlig hjælp til forsikringsspørgsmål på dansk."}],["$","meta","2",{"name":"author","content":"AI Forsikringsguiden Team"}],["$","meta","3",{"name":"keywords","content":"forsikring, AI, rådgivning, danmark, bilforsikring, husejerforsikring, indboforsikring"}],["$","meta","4",{"name":"robots","content":"index, follow"}],["$","meta","5",{"property":"og:title","content":"AI Forsikringsguiden"}],["$","meta","6",{"property":"og:description","content":"Intelligent dansk forsikringsrådgivning med AI"}],["$","meta","7",{"property":"og:site_name","content":"AI Forsikringsguiden"}],["$","meta","8",{"property":"og:locale","content":"da_DK"}],["$","meta","9",{"property":"og:type","content":"website"}],["$","meta","10",{"name":"twitter:card","content":"summary"}],["$","meta","11",{"name":"twitter:title","content":"AI Forsikringsguiden"}],["$","meta","12",{"name":"twitter:description","content":"Intelligent dansk forsikringsrådgivning med AI"}]],"error":null,"digest":"$undefined"}
e:{"metadata":"$14:metadata","error":null,"digest":"$undefined"}
