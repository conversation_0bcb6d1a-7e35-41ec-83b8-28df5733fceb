{"name": "@supabase/auth-helpers-react", "version": "0.5.0", "main": "dist/index.js", "types": "dist/index.d.ts", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/supabase/auth-helpers.git"}, "keywords": ["Supabase", "<PERSON><PERSON>", "React"], "author": "Supabase", "license": "MIT", "devDependencies": {"@supabase/supabase-js": "2.42.0", "@types/react": "^17.0.65", "@types/react-dom": "^17.0.20", "react": "^17.0.2", "react-dom": "^17.0.2", "rimraf": "^4.4.1", "tsup": "^6.7.0", "typescript": "^4.9.5", "config": "0.1.0", "tsconfig": "0.1.1"}, "peerDependencies": {"@supabase/supabase-js": "^2.39.8"}, "scripts": {"lint": "tsc", "build": "tsup", "clean:all": "rimraf dist node_modules"}}