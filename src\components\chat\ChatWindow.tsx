'use client'

import { useState } from 'react'
import ChatInput from './ChatInput'
import ErrorBoundary, { ChatErrorFallback } from '../ErrorBoundary'
import { ChatLoadingSpinner } from '../LoadingSpinner'

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
}

export default function ChatWindow() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'Hej! Jeg er din AI forsikringsassistent. Hvordan kan jeg hjælpe dig i dag?',
      role: 'assistant',
      timestamp: new Date()
    }
  ])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSendMessage = async (content: string) => {
    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      role: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)
    setError(null)

    try {
      // Check if OpenAI API key is configured
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [...messages, userMessage].map(m => ({
            role: m.role,
            content: m.content
          })),
          userDocuments: [] // TODO: Implement document context
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 500 && data.error?.includes('API key')) {
          throw new Error('OpenAI API nøgle er ikke konfigureret. Kontakt administrator.')
        }
        if (response.status === 401) {
          throw new Error('Ugyldig API nøgle. Kontakt administrator.')
        }
        if (response.status === 429) {
          throw new Error('For mange forespørgsler. Prøv igen om lidt.')
        }
        throw new Error(data.error || 'Ukendt fejl fra serveren')
      }

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: data.message,
        role: 'assistant',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error sending message:', error)
      const errorText = error instanceof Error ? error.message : 'Der opstod en uventet fejl'
      setError(errorText)

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `❌ ${errorText}`,
        role: 'assistant',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <ErrorBoundary fallback={ChatErrorFallback}>
      <div className="card max-w-4xl mx-auto">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Chat med AI Forsikringsassistent
        </h2>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-lg">
            <p className="text-red-700 text-sm">
              <strong>Fejl:</strong> {error}
            </p>
          </div>
        )}

        <div className="h-96 overflow-y-auto mb-4 border rounded-lg p-4 bg-gray-50">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`mb-4 ${
                message.role === 'user' ? 'text-right' : 'text-left'
              }`}
            >
              <div
                className={`inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-insurance-blue text-white'
                    : 'bg-white text-gray-900 border'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString('da-DK', {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="mb-4 text-left">
              <div className="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-white border">
                <ChatLoadingSpinner />
              </div>
            </div>
          )}
        </div>

        <ChatInput onSendMessage={handleSendMessage} disabled={isLoading} />
      </div>
    </ErrorBoundary>
  )
}