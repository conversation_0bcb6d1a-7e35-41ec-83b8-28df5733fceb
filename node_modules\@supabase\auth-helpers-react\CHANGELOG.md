# @supabase/auth-helpers-react

## 0.5.0

### Minor Changes

- 9e7ff76: upgrade supabase-js version to v2.42.0

## 0.4.2

### Patch Changes

- 4a7bbfe: Update session when updating user

## 0.4.1

### Patch Changes

- 1dc9c9c: fix: Handle initialSession changes in context provider

## 0.4.0

### Minor Changes

- 65bf8d4: Add cookie storage adapter per library following a shared storage adapter
- 65bf8d4: Add full server side support to auth helpers through PKCE

## 0.4.0-next.0

### Minor Changes

- 65bf8d4: Add cookie storage adapter per library following a shared storage adapter
- 65bf8d4: Add full server side support to auth helpers through PKCE

## 0.3.1

### Patch Changes

- 2fda843: add missing supabase-js peerDependency
- 2fda843: remove auth-helpers-shared dependency in react package
- 2fda843: update supabase-js

## 0.3.0

### Minor Changes

- fd30e33: Update to work with supabase-js v2 RC

### Patch Changes

- 3154f16: fix: typing for useSupabaseClient.
- fe5c4a6: chore: improve types.
- 2fdb094: chore: types and middleware improvements.
- Updated dependencies [20fa944]
- Updated dependencies [fd30e33]
- Updated dependencies [fe5c4a6]
- Updated dependencies [2fdb094]
  - @supabase/auth-helpers-shared@0.2.0

## 0.3.0-next.4

### Patch Changes

- Updated dependencies [20fa944]
  - @supabase/auth-helpers-shared@0.2.0-next.3

## 0.3.0-next.3

### Patch Changes

- 3154f16: fix: typing for useSupabaseClient.

## 0.3.0-next.2

### Patch Changes

- 2fdb094: chore: types and middleware improvements.
- Updated dependencies [2fdb094]
  - @supabase/auth-helpers-shared@0.2.0-next.2

## 0.3.0-next.1

### Patch Changes

- fe5c4a6: chore: improve types.
- Updated dependencies [fe5c4a6]
  - @supabase/auth-helpers-shared@0.2.0-next.1

## 0.3.0-next.0

### Minor Changes

- 1b33e44: Update to work with supabase-js v2 RC

### Patch Changes

- Updated dependencies [1b33e44]
  - @supabase/auth-helpers-shared@0.2.0-next.0

## 0.2.3

### Patch Changes

- Updated dependencies [56228e3]
  - @supabase/auth-helpers-shared@0.1.3

## 0.2.2

### Patch Changes

- Updated dependencies [38ccf1c]
  - @supabase/auth-helpers-shared@0.1.2

## 0.2.1

### Patch Changes

- 9dda264: Add better error handling and error codes
- 9dda264: Fix no cookie found issue
- Updated dependencies [9dda264]
  - @supabase/auth-helpers-shared@0.1.1

## 0.2.0

### Minor Changes

- f399820: Using shared package as a dependency
  Update sveltekit package with latest code to update tokens

## 0.1.0

### Minor Changes

- a3c2991: Initial release of new library version
