import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import fs from 'fs'
import path from 'path'

// Only initialize OpenAI if API key is available
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
}) : null

// Load the expert prompt
function getExpertPrompt(): string {
  try {
    const promptPath = path.join(process.cwd(), 'prompts', 'expertPrompt.txt')
    return fs.readFileSync(promptPath, 'utf-8')
  } catch (error) {
    console.error('Failed to load expert prompt:', error)
    return 'Du er en hjælpsom dansk forsikringsrådgiver. Svar på dansk og vær præcis og hjælpsom.'
  }
}

// Rate limiting (simple in-memory implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(ip: string): boolean {
  const now = Date.now()
  const windowMs = 60 * 1000 // 1 minute
  const maxRequests = 10 // 10 requests per minute

  const current = rateLimitMap.get(ip)

  if (!current || now > current.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (current.count >= maxRequests) {
    return false
  }

  current.count++
  return true
}

function sanitizeInput(input: string): string {
  // Basic input sanitization
  return input
    .trim()
    .replace(/[<>"'&]/g, '') // Remove potentially dangerous characters
    .substring(0, 4000) // Limit length
}

function validateMessages(messages: any[]): boolean {
  if (!Array.isArray(messages) || messages.length === 0) {
    return false
  }

  return messages.every(msg =>
    msg &&
    typeof msg.role === 'string' &&
    ['user', 'assistant'].includes(msg.role) &&
    typeof msg.content === 'string' &&
    msg.content.trim().length > 0 &&
    msg.content.length <= 4000
  )
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') ||
              request.headers.get('x-real-ip') ||
              'unknown'

    // Check rate limit
    if (!checkRateLimit(ip)) {
      return NextResponse.json(
        { error: 'For mange forespørgsler. Prøv igen om lidt.' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { messages, userDocuments } = body

    // Validate input
    if (!validateMessages(messages)) {
      return NextResponse.json(
        { error: 'Ugyldige beskeder. Kontroller dit input.' },
        { status: 400 }
      )
    }

    if (!process.env.OPENAI_API_KEY) {
      console.error('OpenAI API key not configured')
      return NextResponse.json(
        { error: 'AI-tjenesten er ikke konfigureret korrekt. Kontakt administrator.' },
        { status: 500 }
      )
    }

    // Get the expert prompt
    const systemPrompt = getExpertPrompt()

    // Prepare context with user documents if available
    let contextualPrompt = systemPrompt
    if (userDocuments && userDocuments.length > 0) {
      contextualPrompt += '\n\n## BRUGERENS DOKUMENTER\n'
      userDocuments.forEach((doc: any, index: number) => {
        contextualPrompt += `\nDokument ${index + 1}: ${doc.name}\n${doc.content}\n`
      })
      contextualPrompt += '\nReferencér til disse dokumenter i dit svar når relevant.\n'
    }

    // Sanitize and prepare messages for OpenAI
    const chatMessages = [
      {
        role: 'system' as const,
        content: contextualPrompt
      },
      ...messages.map((msg: any) => ({
        role: msg.role as 'user' | 'assistant',
        content: sanitizeInput(msg.content)
      }))
    ]

    const completion = await openai!.chat.completions.create({
      model: 'gpt-4',
      messages: chatMessages,
      temperature: 0.3,
      max_tokens: 2000,
      presence_penalty: 0.1,
      frequency_penalty: 0.1,
      user: ip // For OpenAI's abuse monitoring
    })

    const response = completion.choices[0]?.message?.content

    if (!response) {
      return NextResponse.json(
        { error: 'No response generated' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: response,
      usage: completion.usage
    })

  } catch (error) {
    console.error('Chat API error:', error)

    // Handle specific OpenAI errors
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'Ugyldig API nøgle. Kontakt administrator.' },
          { status: 401 }
        )
      }

      if (error.message.includes('rate limit')) {
        return NextResponse.json(
          { error: 'OpenAI rate limit nået. Prøv igen senere.' },
          { status: 429 }
        )
      }

      if (error.message.includes('quota')) {
        return NextResponse.json(
          { error: 'AI-tjenesten har nået sin kvote. Kontakt administrator.' },
          { status: 503 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Der opstod en intern serverfejl. Prøv igen senere.' },
      { status: 500 }
    )
  }
}