'use client'

import { useState, useEffect } from 'react'
import { useInsuranceStore } from '@/lib/store/insuranceStore'
import { ProactiveInsuranceAgent } from '@/lib/ai/ProactiveAgent'
import type { AIInsight } from '@/lib/supabase/client'

interface InsightCardProps {
  insight: AIInsight
  onAcknowledge: (id: string) => void
}

function InsightCard({ insight, onAcknowledge }: InsightCardProps) {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50'
      case 'medium': return 'border-yellow-200 bg-yellow-50'
      case 'urgent': return 'border-red-300 bg-red-100'
      default: return 'border-blue-200 bg-blue-50'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return '🔴'
      case 'medium': return '🟡'
      case 'urgent': return '🚨'
      default: return '🔵'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'coverage_gap': return '🛡️'
      case 'cost_optimization': return '💰'
      case 'renewal_reminder': return '📅'
      case 'claim_opportunity': return '📝'
      case 'risk_alert': return '⚠️'
      default: return '💡'
    }
  }

  return (
    <div className={`border rounded-lg p-4 ${getPriorityColor(insight.priority)}`}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className="text-xl">{getTypeIcon(insight.insight_type)}</span>
          <span className="text-lg">{getPriorityIcon(insight.priority)}</span>
          <h3 className="font-semibold text-gray-900">{insight.title}</h3>
        </div>
        {!insight.is_acknowledged && (
          <button
            onClick={() => onAcknowledge(insight.id)}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            ✓ Markér som læst
          </button>
        )}
      </div>
      
      <p className="text-gray-700 mb-4">{insight.description}</p>
      
      {insight.recommended_actions && Array.isArray(insight.recommended_actions) && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Anbefalede handlinger:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            {(insight.recommended_actions as string[]).map((action, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span>•</span>
                <span>{action}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="flex items-center justify-between text-xs text-gray-500">
        <span>
          Sikkerhed: {Math.round((insight.confidence_score || 0) * 100)}%
        </span>
        <span>
          {new Date(insight.created_at).toLocaleDateString('da-DK')}
        </span>
      </div>
    </div>
  )
}

export default function InsightsDashboard() {
  const {
    insights,
    user,
    acknowledgeInsight,
    addInsight,
    setLoading,
    isLoading
  } = useInsuranceStore()
  
  const [refreshing, setRefreshing] = useState(false)
  const [filter, setFilter] = useState<'all' | 'unread' | 'high_priority'>('unread')

  const proactiveAgent = new ProactiveInsuranceAgent()

  useEffect(() => {
    if (user && insights.length === 0) {
      refreshInsights()
    }
  }, [user])

  const refreshInsights = async () => {
    if (!user) return

    setRefreshing(true)
    try {
      const proactiveInsights = await proactiveAgent.analyzeUserSituation(user.id)
      const recommendations = await proactiveAgent.generateProactiveRecommendations(proactiveInsights)
      
      // Add each recommendation as an insight
      for (const recommendation of recommendations) {
        addInsight(recommendation as AIInsight)
      }
      
      // Save to database
      await proactiveAgent.saveInsights(user.id, recommendations as AIInsight[])
    } catch (error) {
      console.error('Error refreshing insights:', error)
    } finally {
      setRefreshing(false)
    }
  }

  const handleAcknowledge = async (insightId: string) => {
    acknowledgeInsight(insightId)
    // In a real app, you'd also update the database here
  }

  const filteredInsights = insights.filter(insight => {
    switch (filter) {
      case 'unread':
        return !insight.is_acknowledged
      case 'high_priority':
        return insight.priority === 'high' || insight.priority === 'urgent'
      default:
        return true
    }
  })

  const stats = {
    total: insights.length,
    unread: insights.filter(i => !i.is_acknowledged).length,
    highPriority: insights.filter(i => i.priority === 'high' || i.priority === 'urgent').length,
    costSavings: insights
      .filter(i => i.insight_type === 'cost_optimization')
      .reduce((total, i) => {
        const saving = (i.data_sources as any)?.saving || 0
        return total + saving
      }, 0)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-insurance-blue"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">AI Indsigter</h2>
          <p className="text-gray-600">
            Personaliserede anbefalinger baseret på din forsikringssituation
          </p>
        </div>
        <button
          onClick={refreshInsights}
          disabled={refreshing}
          className="btn-primary disabled:opacity-50"
        >
          {refreshing ? (
            <span className="flex items-center space-x-2">
              <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              <span>Opdaterer...</span>
            </span>
          ) : (
            'Opdater indsigter'
          )}
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border p-4">
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-sm text-gray-600">Totale indsigter</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-2xl font-bold text-orange-600">{stats.unread}</div>
          <div className="text-sm text-gray-600">Ulæste</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-2xl font-bold text-red-600">{stats.highPriority}</div>
          <div className="text-sm text-gray-600">Høj prioritet</div>
        </div>
        <div className="bg-white rounded-lg border p-4">
          <div className="text-2xl font-bold text-green-600">
            {Math.round(stats.costSavings).toLocaleString()} kr
          </div>
          <div className="text-sm text-gray-600">Potentielle besparelser</div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex space-x-2">
        <button
          onClick={() => setFilter('all')}
          className={`px-4 py-2 rounded-lg text-sm font-medium ${
            filter === 'all' 
              ? 'bg-insurance-blue text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          Alle ({stats.total})
        </button>
        <button
          onClick={() => setFilter('unread')}
          className={`px-4 py-2 rounded-lg text-sm font-medium ${
            filter === 'unread' 
              ? 'bg-insurance-blue text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          Ulæste ({stats.unread})
        </button>
        <button
          onClick={() => setFilter('high_priority')}
          className={`px-4 py-2 rounded-lg text-sm font-medium ${
            filter === 'high_priority' 
              ? 'bg-insurance-blue text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          Høj prioritet ({stats.highPriority})
        </button>
      </div>

      {/* Insights */}
      {filteredInsights.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🎉</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {filter === 'unread' ? 'Ingen ulæste indsigter' : 'Ingen indsigter'}
          </h3>
          <p className="text-gray-600">
            {filter === 'unread' 
              ? 'Du har læst alle dine indsigter. Godt arbejde!'
              : 'Klik på "Opdater indsigter" for at få AI-genererede anbefalinger.'}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredInsights.map((insight) => (
            <InsightCard
              key={insight.id}
              insight={insight}
              onAcknowledge={handleAcknowledge}
            />
          ))}
        </div>
      )}
    </div>
  )
} 