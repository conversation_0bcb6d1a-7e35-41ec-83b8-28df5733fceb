<!DOCTYPE html><html lang="da"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/033286cb7e57f891.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-7829b3ab3911a1da.js"/><script src="/_next/static/chunks/4bd1b696-18452535c1c4862d.js" async=""></script><script src="/_next/static/chunks/684-e11163ca66e1bc9c.js" async=""></script><script src="/_next/static/chunks/main-app-3de6dea1802fdcb8.js" async=""></script><script src="/_next/static/chunks/app/page-4886ad9beb70cfdd.js" async=""></script><title>AI Forsikringsguiden</title><meta name="description" content="Digital AI-assistent til forsikringsrelaterede henvendelser og analyser"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div class="min-h-screen flex flex-col"><header class="bg-white shadow-sm border-b"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between items-center h-16"><div class="flex items-center"><h1 class="text-xl font-bold text-insurance-blue">AI Forsikringsguiden</h1></div><nav class="flex space-x-4"><a href="#" class="text-gray-600 hover:text-insurance-blue">Chat</a><a href="#" class="text-gray-600 hover:text-insurance-blue">Mine Dokumenter</a><a href="#" class="text-gray-600 hover:text-insurance-blue">Hjælp</a></nav></div></div></header><main class="flex-1"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="mb-8"><h1 class="text-3xl font-bold text-gray-900 mb-4">Velkommen til AI Forsikringsguiden</h1><p class="text-lg text-gray-600 mb-6">Få hjælp til forsikringsspørgsmål, analyse af policer og juridisk vejledning fra din AI-assistent.</p><div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"><div class="card"><h3 class="text-lg font-semibold text-gray-900 mb-2">Forsikringsrådgivning</h3><p class="text-gray-600">Få hjælp til at vælge den rigtige forsikring og forstå dine muligheder.</p></div><div class="card"><h3 class="text-lg font-semibold text-gray-900 mb-2">Dokumentanalyse</h3><p class="text-gray-600">Upload dine forsikringspapirer og få dem forklaret på dansk.</p></div><div class="card"><h3 class="text-lg font-semibold text-gray-900 mb-2">Juridisk Vejledning</h3><p class="text-gray-600">Forstå dine rettigheder og få guidance i forsikringstvister.</p></div></div></div><div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"><div><h2 class="text-xl font-semibold text-gray-900 mb-4">Upload Dokumenter</h2><div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-insurance-blue transition-colors"><div class="space-y-4"><div class="mx-auto w-12 h-12 text-gray-400"><svg fill="none" stroke="currentColor" viewBox="0 0 48 48"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"></path></svg></div><div><label for="file-upload" class="cursor-pointer"><span class="text-sm font-medium text-insurance-blue hover:text-insurance-dark">Upload forsikringsdokument</span><input id="file-upload" type="file" class="sr-only" accept=".pdf,.txt,.jpg,.jpeg,.png" name="file-upload"/></label><p class="text-xs text-gray-500 mt-1">PDF, tekst eller billede filer op til 10MB</p></div></div></div></div><div><h2 class="text-xl font-semibold text-gray-900 mb-4">Mine Dokumenter</h2><div class="card text-center py-8"><div class="text-gray-400 text-4xl mb-4">📄</div><h3 class="text-lg font-medium text-gray-900 mb-2">Ingen dokumenter uploadet</h3><p class="text-gray-600">Upload forsikringsdokumenter for at få personaliseret hjælp</p></div></div></div><div class="card max-w-4xl mx-auto"><h2 class="text-xl font-semibold text-gray-900 mb-4">Chat med AI Forsikringsassistent</h2><div class="h-96 overflow-y-auto mb-4 border rounded-lg p-4 bg-gray-50"><div class="mb-4 text-left"><div class="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-white text-gray-900 border"><p class="text-sm">Hej! Jeg er din AI forsikringsassistent. Hvordan kan jeg hjælpe dig i dag?</p><p class="text-xs opacity-70 mt-1">21.05</p></div></div></div><form class="flex space-x-2"><div class="flex-1"><textarea placeholder="Skriv dit forsikringsspørgsmål her..." class="input-field resize-none h-12" rows="1"></textarea></div><button type="submit" disabled="" class="btn-primary px-6 disabled:opacity-50 disabled:cursor-not-allowed">Send</button></form></div></div><!--$--><!--/$--><!--$--><!--/$--></main><footer class="bg-gray-100 border-t"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4"><p class="text-sm text-gray-600 text-center">AI Forsikringsguiden - Ikke juridisk rådgivning. Konsulter altid en advokat ved komplekse sager.</p></div></footer></div><script src="/_next/static/chunks/webpack-7829b3ab3911a1da.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[9830,[\"974\",\"static/chunks/app/page-4886ad9beb70cfdd.js\"],\"default\"]\n8:I[9665,[],\"MetadataBoundary\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/033286cb7e57f891.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"fBru3ALuIsa1oi8HKXLts\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/033286cb7e57f891.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"da\",\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"div\",null,{\"className\":\"min-h-screen flex flex-col\",\"children\":[[\"$\",\"header\",null,{\"className\":\"bg-white shadow-sm border-b\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex justify-between items-center h-16\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center\",\"children\":[\"$\",\"h1\",null,{\"className\":\"text-xl font-bold text-insurance-blue\",\"children\":\"AI Forsikringsguiden\"}]}],[\"$\",\"nav\",null,{\"className\":\"flex space-x-4\",\"children\":[[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-gray-600 hover:text-insurance-blue\",\"children\":\"Chat\"}],[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-gray-600 hover:text-insurance-blue\",\"children\":\"Mine Dokumenter\"}],[\"$\",\"a\",null,{\"href\":\"#\",\"className\":\"text-gray-600 hover:text-insurance-blue\",\"children\":\"Hjælp\"}]]}]]}]}]}],[\"$\",\"main\",null,{\"className\":\"flex-1\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"footer\",null,{\"className\":\"bg-gray-100 border-t\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-sm text-gray-600 text-center\",\"children\":\"AI Forsikringsguiden - Ikke juridisk rådgivning. Konsulter altid en advokat ved komplekse sager.\"}]}]}]]}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"F1mXA981ODU_oV-JijFS2\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n9:[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"AI Forsikringsguiden\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Digital AI-assistent til forsikringsrelaterede henvendelser og analyser\"}]],\"error\":null,\"digest\":\"$undefined\"}\ne:{\"metadata\":\"$14:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>