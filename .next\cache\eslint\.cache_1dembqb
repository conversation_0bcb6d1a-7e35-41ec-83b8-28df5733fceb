[{"C:\\Projects\\AI forsikrings guiden\\src\\app\\api\\chat\\route.ts": "1", "C:\\Projects\\AI forsikrings guiden\\src\\app\\layout.tsx": "2", "C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx": "3", "C:\\Projects\\AI forsikrings guiden\\src\\components\\chat\\ChatInput.tsx": "4", "C:\\Projects\\AI forsikrings guiden\\src\\components\\chat\\ChatWindow.tsx": "5", "C:\\Projects\\AI forsikrings guiden\\src\\components\\consent\\ConsentModal.tsx": "6", "C:\\Projects\\AI forsikrings guiden\\src\\components\\dashboard\\InsightsDashboard.tsx": "7", "C:\\Projects\\AI forsikrings guiden\\src\\components\\DocumentViewer.tsx": "8", "C:\\Projects\\AI forsikrings guiden\\src\\components\\ErrorBoundary.tsx": "9", "C:\\Projects\\AI forsikrings guiden\\src\\components\\LoadingSpinner.tsx": "10", "C:\\Projects\\AI forsikrings guiden\\src\\components\\PDFUpload.tsx": "11", "C:\\Projects\\AI forsikrings guiden\\src\\lib\\agent\\AgentController.ts": "12", "C:\\Projects\\AI forsikrings guiden\\src\\lib\\ai\\AgentOrchestrator.ts": "13", "C:\\Projects\\AI forsikrings guiden\\src\\lib\\ai\\ProactiveAgent.ts": "14", "C:\\Projects\\AI forsikrings guiden\\src\\lib\\auth\\AuthProvider.tsx": "15", "C:\\Projects\\AI forsikrings guiden\\src\\lib\\document\\DocumentProcessor.ts": "16", "C:\\Projects\\AI forsikrings guiden\\src\\lib\\store\\insuranceStore.ts": "17", "C:\\Projects\\AI forsikrings guiden\\src\\lib\\supabase\\client.ts": "18", "C:\\Projects\\AI forsikrings guiden\\src\\lib\\supabase\\types.ts": "19"}, {"size": 5771, "mtime": 1749588519336, "results": "20", "hashOfConfig": "21"}, {"size": 7562, "mtime": 1749588838996, "results": "22", "hashOfConfig": "21"}, {"size": 7429, "mtime": 1749588891748, "results": "23", "hashOfConfig": "21"}, {"size": 2156, "mtime": 1749587947850, "results": "24", "hashOfConfig": "21"}, {"size": 4746, "mtime": 1749587911120, "results": "25", "hashOfConfig": "21"}, {"size": 14494, "mtime": 1749586814692, "results": "26", "hashOfConfig": "21"}, {"size": 9715, "mtime": 1749587261705, "results": "27", "hashOfConfig": "21"}, {"size": 5788, "mtime": 1749584948931, "results": "28", "hashOfConfig": "21"}, {"size": 3269, "mtime": 1749588912369, "results": "29", "hashOfConfig": "21"}, {"size": 2160, "mtime": 1749587874919, "results": "30", "hashOfConfig": "21"}, {"size": 6356, "mtime": 1749588037127, "results": "31", "hashOfConfig": "21"}, {"size": 7145, "mtime": 1749584949389, "results": "32", "hashOfConfig": "21"}, {"size": 15485, "mtime": 1749586936897, "results": "33", "hashOfConfig": "21"}, {"size": 15865, "mtime": 1749587005334, "results": "34", "hashOfConfig": "21"}, {"size": 5951, "mtime": 1749586752080, "results": "35", "hashOfConfig": "21"}, {"size": 11355, "mtime": 1749589023458, "results": "36", "hashOfConfig": "21"}, {"size": 11723, "mtime": 1749588756015, "results": "37", "hashOfConfig": "21"}, {"size": 3753, "mtime": 1749588592454, "results": "38", "hashOfConfig": "21"}, {"size": 19674, "mtime": 1749586724947, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1pssldw", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Projects\\AI forsikrings guiden\\src\\app\\api\\chat\\route.ts", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\app\\layout.tsx", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\app\\page.tsx", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\components\\chat\\ChatInput.tsx", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\components\\chat\\ChatWindow.tsx", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\components\\consent\\ConsentModal.tsx", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\components\\dashboard\\InsightsDashboard.tsx", ["97", "98", "99"], [], "C:\\Projects\\AI forsikrings guiden\\src\\components\\DocumentViewer.tsx", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\components\\LoadingSpinner.tsx", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\components\\PDFUpload.tsx", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\lib\\agent\\AgentController.ts", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\lib\\ai\\AgentOrchestrator.ts", ["100", "101", "102", "103", "104", "105", "106", "107"], [], "C:\\Projects\\AI forsikrings guiden\\src\\lib\\ai\\ProactiveAgent.ts", ["108", "109", "110", "111", "112", "113", "114"], [], "C:\\Projects\\AI forsikrings guiden\\src\\lib\\auth\\AuthProvider.tsx", ["115", "116", "117", "118"], [], "C:\\Projects\\AI forsikrings guiden\\src\\lib\\document\\DocumentProcessor.ts", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\lib\\store\\insuranceStore.ts", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\lib\\supabase\\client.ts", [], [], "C:\\Projects\\AI forsikrings guiden\\src\\lib\\supabase\\types.ts", [], [], {"ruleId": "119", "severity": 2, "message": "120", "line": 95, "column": 5, "nodeType": null, "messageId": "121", "endLine": 95, "endColumn": 15}, {"ruleId": "122", "severity": 1, "message": "123", "line": 108, "column": 6, "nodeType": "124", "endLine": 108, "endColumn": 12, "suggestions": "125"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 155, "column": 43, "nodeType": "128", "messageId": "129", "endLine": 155, "endColumn": 46, "suggestions": "130"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 7, "column": 17, "nodeType": "128", "messageId": "129", "endLine": 7, "endColumn": 20, "suggestions": "131"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 19, "column": 28, "nodeType": "128", "messageId": "129", "endLine": 19, "endColumn": 31, "suggestions": "132"}, {"ruleId": "119", "severity": 2, "message": "133", "line": 104, "column": 14, "nodeType": null, "messageId": "121", "endLine": 104, "endColumn": 19}, {"ruleId": "119", "severity": 2, "message": "134", "line": 196, "column": 43, "nodeType": null, "messageId": "121", "endLine": 196, "endColumn": 50}, {"ruleId": "119", "severity": 2, "message": "134", "line": 248, "column": 43, "nodeType": null, "messageId": "121", "endLine": 248, "endColumn": 50}, {"ruleId": "119", "severity": 2, "message": "134", "line": 299, "column": 48, "nodeType": null, "messageId": "121", "endLine": 299, "endColumn": 55}, {"ruleId": "119", "severity": 2, "message": "134", "line": 350, "column": 5, "nodeType": null, "messageId": "121", "endLine": 350, "endColumn": 12}, {"ruleId": "119", "severity": 2, "message": "135", "line": 445, "column": 36, "nodeType": null, "messageId": "121", "endLine": 445, "endColumn": 41}, {"ruleId": "126", "severity": 2, "message": "127", "line": 45, "column": 15, "nodeType": "128", "messageId": "129", "endLine": 45, "endColumn": 18, "suggestions": "136"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 46, "column": 24, "nodeType": "128", "messageId": "129", "endLine": 46, "endColumn": 27, "suggestions": "137"}, {"ruleId": "119", "severity": 2, "message": "138", "line": 63, "column": 44, "nodeType": null, "messageId": "121", "endLine": 63, "endColumn": 50}, {"ruleId": "126", "severity": 2, "message": "127", "line": 144, "column": 42, "nodeType": "128", "messageId": "129", "endLine": 144, "endColumn": 45, "suggestions": "139"}, {"ruleId": "126", "severity": 2, "message": "127", "line": 261, "column": 18, "nodeType": "128", "messageId": "129", "endLine": 261, "endColumn": 21, "suggestions": "140"}, {"ruleId": "119", "severity": 2, "message": "141", "line": 262, "column": 5, "nodeType": null, "messageId": "121", "endLine": 262, "endColumn": 14}, {"ruleId": "126", "severity": 2, "message": "127", "line": 430, "column": 57, "nodeType": "128", "messageId": "129", "endLine": 430, "endColumn": 60, "suggestions": "142"}, {"ruleId": "122", "severity": 1, "message": "143", "line": 62, "column": 6, "nodeType": "124", "endLine": 62, "endColumn": 8, "suggestions": "144"}, {"ruleId": "119", "severity": 2, "message": "133", "line": 95, "column": 14, "nodeType": null, "messageId": "121", "endLine": 95, "endColumn": 19}, {"ruleId": "119", "severity": 2, "message": "133", "line": 135, "column": 14, "nodeType": null, "messageId": "121", "endLine": 135, "endColumn": 19}, {"ruleId": "119", "severity": 2, "message": "133", "line": 165, "column": 14, "nodeType": null, "messageId": "121", "endLine": 165, "endColumn": 19}, "@typescript-eslint/no-unused-vars", "'setLoading' is assigned a value but never used.", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'insights.length' and 'refreshInsights'. Either include them or remove the dependency array.", "ArrayExpression", ["145"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["146", "147"], ["148", "149"], ["150", "151"], "'error' is defined but never used.", "'context' is defined but never used.", "'query' is defined but never used.", ["152", "153"], ["154", "155"], "'claims' is assigned a value but never used.", ["156", "157"], ["158", "159"], "'documents' is defined but never used.", ["160", "161"], "React Hook useEffect has missing dependencies: 'fetchUserProfile' and 'supabase.auth'. Either include them or remove the dependency array.", ["162"], {"desc": "163", "fix": "164"}, {"messageId": "165", "fix": "166", "desc": "167"}, {"messageId": "168", "fix": "169", "desc": "170"}, {"messageId": "165", "fix": "171", "desc": "167"}, {"messageId": "168", "fix": "172", "desc": "170"}, {"messageId": "165", "fix": "173", "desc": "167"}, {"messageId": "168", "fix": "174", "desc": "170"}, {"messageId": "165", "fix": "175", "desc": "167"}, {"messageId": "168", "fix": "176", "desc": "170"}, {"messageId": "165", "fix": "177", "desc": "167"}, {"messageId": "168", "fix": "178", "desc": "170"}, {"messageId": "165", "fix": "179", "desc": "167"}, {"messageId": "168", "fix": "180", "desc": "170"}, {"messageId": "165", "fix": "181", "desc": "167"}, {"messageId": "168", "fix": "182", "desc": "170"}, {"messageId": "165", "fix": "183", "desc": "167"}, {"messageId": "168", "fix": "184", "desc": "170"}, {"desc": "185", "fix": "186"}, "Update the dependencies array to be: [insights.length, refreshInsights, user]", {"range": "187", "text": "188"}, "suggestUnknown", {"range": "189", "text": "190"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "191", "text": "192"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "193", "text": "190"}, {"range": "194", "text": "192"}, {"range": "195", "text": "190"}, {"range": "196", "text": "192"}, {"range": "197", "text": "190"}, {"range": "198", "text": "192"}, {"range": "199", "text": "190"}, {"range": "200", "text": "192"}, {"range": "201", "text": "190"}, {"range": "202", "text": "192"}, {"range": "203", "text": "190"}, {"range": "204", "text": "192"}, {"range": "205", "text": "190"}, {"range": "206", "text": "192"}, "Update the dependencies array to be: [fetchUserProfile, supabase.auth]", {"range": "207", "text": "208"}, [3494, 3500], "[insights.length, refreshInsights, user]", [5051, 5054], "unknown", [5051, 5054], "never", [214, 217], [214, 217], [509, 512], [509, 512], [1156, 1159], [1156, 1159], [1186, 1189], [1186, 1189], [4246, 4249], [4246, 4249], [8688, 8691], [8688, 8691], [14657, 14660], [14657, 14660], [1972, 1974], "[fetchUserProfile, supabase.auth]"]