exports.id=845,exports.ids=[845],exports.modules={1135:()=>{},3636:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},4431:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d,metadata:()=>c});var i=r(7413),n=r(4536),t=r.n(n);r(1135);var l=r(5041),a=r.n(l);let c={title:"AI Forsikringsguiden | Intelligent dansk forsikringsr\xe5dgivning",description:"F\xe5 ekspert forsikringsr\xe5dgivning med AI. Upload dokumenter, stil sp\xf8rgsm\xe5l, og f\xe5 personlig hj\xe6lp til forsikringssp\xf8rgsm\xe5l p\xe5 dansk.",keywords:"forsikring, AI, r\xe5dgivning, danmark, bilforsikring, husejerforsikring, indboforsikring",authors:[{name:"AI Forsikringsguiden Team"}],viewport:"width=device-width, initial-scale=1",robots:"index, follow",openGraph:{title:"AI Forsikringsguiden",description:"Intelligent dansk forsikringsr\xe5dgivning med AI",type:"website",locale:"da_DK",siteName:"AI Forsikringsguiden"}};function d({children:e}){return(0,i.jsx)("html",{lang:"da",children:(0,i.jsx)("body",{className:`${a().className} antialiased bg-gray-50 min-h-screen`,children:(0,i.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,i.jsx)("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-50",children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,i.jsx)("div",{className:"flex items-center space-x-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-insurance-blue rounded-lg flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,i.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Forsikringsguiden"})]})}),(0,i.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,i.jsx)(t(),{href:"/",className:"text-gray-600 hover:text-insurance-blue transition-colors",children:"Hjem"}),(0,i.jsx)(t(),{href:"/chat",className:"text-gray-600 hover:text-insurance-blue transition-colors",children:"Chat"}),(0,i.jsx)(t(),{href:"/dashboard",className:"text-gray-600 hover:text-insurance-blue transition-colors",children:"Dashboard"}),(0,i.jsx)("a",{href:"/policies",className:"text-gray-600 hover:text-insurance-blue transition-colors",children:"Forsikringer"}),(0,i.jsx)("a",{href:"/documents",className:"text-gray-600 hover:text-insurance-blue transition-colors",children:"Dokumenter"})]}),(0,i.jsx)("div",{className:"flex items-center space-x-4",children:(0,i.jsx)("button",{className:"text-gray-600 hover:text-insurance-blue",title:"Brugerindstillinger","aria-label":"\xc5bn brugerindstillinger",children:(0,i.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-5-5 5-5m-5 5H9"})})})})]})})}),(0,i.jsx)("main",{className:"flex-1",children:e}),(0,i.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-auto",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-4",children:"AI Forsikringsguiden"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Intelligent forsikringsr\xe5dgivning p\xe5 dansk med respekt for dit privatliv."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-4",children:"Tjenester"}),(0,i.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/chat",className:"hover:text-insurance-blue",children:"AI Chat"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/documents",className:"hover:text-insurance-blue",children:"Dokumentanalyse"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/comparison",className:"hover:text-insurance-blue",children:"Prissammenligning"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/claims",className:"hover:text-insurance-blue",children:"Skadeh\xe5ndtering"})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-4",children:"Support"}),(0,i.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/help",className:"hover:text-insurance-blue",children:"Hj\xe6lp & FAQ"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/contact",className:"hover:text-insurance-blue",children:"Kontakt os"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/feedback",className:"hover:text-insurance-blue",children:"Send feedback"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/status",className:"hover:text-insurance-blue",children:"Service status"})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-4",children:"Juridisk"}),(0,i.jsxs)("ul",{className:"space-y-2 text-sm text-gray-600",children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/privacy",className:"hover:text-insurance-blue",children:"Privatlivspolitik"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/terms",className:"hover:text-insurance-blue",children:"Servicevilk\xe5r"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/gdpr",className:"hover:text-insurance-blue",children:"GDPR"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/cookies",className:"hover:text-insurance-blue",children:"Cookie politik"})})]})]})]}),(0,i.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:(0,i.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"\xa9 2024 AI Forsikringsguiden. Alle rettigheder forbeholdes."}),(0,i.jsxs)("div",{className:"flex items-center space-x-4 mt-4 md:mt-0",children:[(0,i.jsx)("span",{className:"text-xs text-gray-500",children:"Bygget med ❤️ i Danmark"}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,i.jsx)("span",{className:"text-xs text-gray-500",children:"Alle systemer k\xf8rer"})]})]})]})})]})})]})})})}},7604:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},9679:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9927:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))}};