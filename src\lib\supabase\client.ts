import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import type { Database } from './types'

// Client-side Supabase client
export const createClient = () => createClientComponentClient<Database>()

// Server-side Supabase client
export const createServerClient = () => createServerComponentClient<Database>({ cookies })

// Database types
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Specific table types
export type UserProfile = Tables<'user_profiles'>
export type Document = Tables<'documents'>
export type Conversation = Tables<'conversations'>
export type ChatMessage = Tables<'chat_messages'>
export type UserPolicy = Tables<'user_policies'>
export type Claim = Tables<'claims'>
export type AIInsight = Tables<'ai_insights'>
export type Feedback = Tables<'feedback'>
export type UserConsent = Tables<'user_consents'>
export type LegalReference = Tables<'legal_references'>
export type InsuranceCompany = Tables<'insurance_companies'>
export type InsuranceProduct = Tables<'insurance_products'>
export type MarketData = Tables<'market_data'>

// Enums
export type DocumentType = 'insurance_policy' | 'claim_document' | 'correspondence' | 'general_document' | 'legal_document'
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed'
export type MessageRole = 'user' | 'assistant' | 'system'
export type PolicyStatus = 'active' | 'expired' | 'cancelled' | 'pending'
export type ClaimStatus = 'submitted' | 'under_review' | 'approved' | 'denied' | 'settled' | 'closed'
export type InsightPriority = 'low' | 'medium' | 'high' | 'urgent'
export type FeedbackType = 'rating' | 'comment' | 'bug_report' | 'feature_request'
export type ConsentType = 'data_processing' | 'marketing' | 'analytics' | 'document_storage' | 'ai_training'

// Helper functions
export const supabaseErrorHandler = (error: any) => {
  console.error('Supabase error:', error)
  
  if (error?.code === 'PGRST116') {
    return { error: 'Record not found', code: 404 }
  }
  
  if (error?.code === '23505') {
    return { error: 'Record already exists', code: 409 }
  }
  
  if (error?.code === '42501') {
    return { error: 'Insufficient permissions', code: 403 }
  }
  
  return { error: error?.message || 'Unknown database error', code: 500 }
} 